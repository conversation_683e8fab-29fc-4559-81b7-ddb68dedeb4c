
-- First, let's check what enum types already exist and create only the missing ones
DO $$ 
BEGIN
    -- Create service_status enum if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'service_status') THEN
        CREATE TYPE service_status AS ENUM ('Pending', 'In Progress', 'Completed', 'Cancelled', 'On Hold');
    END IF;
    
    -- Create invoice_status enum if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'invoice_status') THEN
        CREATE TYPE invoice_status AS ENUM ('Draft', 'Pending', 'Paid', 'Overdue', 'Cancelled');
    END IF;
    
    -- Create expense_category enum if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'expense_category') THEN
        CREATE TYPE expense_category AS ENUM ('Office', 'Marketing', 'Equipment', 'Utilities', 'Travel', 'Parts', 'Labor', 'Shipping', 'Other');
    END IF;
    
    -- Create receipt_status enum if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'receipt_status') THEN
        CREATE TYPE receipt_status AS ENUM ('Available', 'Pending', 'Missing');
    END IF;
END $$;

-- Create profiles table for user management (only if it doesn't exist)
CREATE TABLE IF NOT EXISTS public.profiles (
  id UUID NOT NULL REFERENCES auth.users ON DELETE CASCADE,
  first_name TEXT,
  last_name TEXT,
  email TEXT,
  phone TEXT,
  role TEXT DEFAULT 'user',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  PRIMARY KEY (id)
);

-- Enable RLS on profiles (safe to run multiple times)
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist to avoid conflicts
DROP POLICY IF EXISTS "Users can view their own profile" ON public.profiles;
DROP POLICY IF EXISTS "Users can update their own profile" ON public.profiles;
DROP POLICY IF EXISTS "Users can insert their own profile" ON public.profiles;

-- RLS policies for profiles
CREATE POLICY "Users can view their own profile" ON public.profiles
  FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update their own profile" ON public.profiles
  FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Users can insert their own profile" ON public.profiles
  FOR INSERT WITH CHECK (auth.uid() = id);

-- Add user_id columns to existing tables (safe with IF NOT EXISTS)
DO $$ 
BEGIN
    -- Add user_id to customers if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'customers' AND column_name = 'user_id') THEN
        ALTER TABLE public.customers ADD COLUMN user_id UUID REFERENCES public.profiles(id);
    END IF;
    
    -- Add user_id to services if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'services' AND column_name = 'user_id') THEN
        ALTER TABLE public.services ADD COLUMN user_id UUID REFERENCES public.profiles(id);
    END IF;
    
    -- Add user_id to invoices if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'invoices' AND column_name = 'user_id') THEN
        ALTER TABLE public.invoices ADD COLUMN user_id UUID REFERENCES public.profiles(id);
    END IF;
    
    -- Add user_id to inventory_items if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'inventory_items' AND column_name = 'user_id') THEN
        ALTER TABLE public.inventory_items ADD COLUMN user_id UUID REFERENCES public.profiles(id);
    END IF;
    
    -- Add user_id to expenses if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'expenses' AND column_name = 'user_id') THEN
        ALTER TABLE public.expenses ADD COLUMN user_id UUID REFERENCES public.profiles(id);
    END IF;
END $$;

-- Add foreign key constraints (safe with IF NOT EXISTS checks)
DO $$
BEGIN
    -- Add services-customer foreign key if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.table_constraints WHERE constraint_name = 'fk_services_customer') THEN
        ALTER TABLE public.services ADD CONSTRAINT fk_services_customer 
          FOREIGN KEY (customer_id) REFERENCES public.customers(id) ON DELETE SET NULL;
    END IF;
    
    -- Add invoices-customer foreign key if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.table_constraints WHERE constraint_name = 'fk_invoices_customer') THEN
        ALTER TABLE public.invoices ADD CONSTRAINT fk_invoices_customer 
          FOREIGN KEY (customer_id) REFERENCES public.customers(id) ON DELETE SET NULL;
    END IF;
    
    -- Add invoices-service foreign key if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.table_constraints WHERE constraint_name = 'fk_invoices_service') THEN
        ALTER TABLE public.invoices ADD CONSTRAINT fk_invoices_service 
          FOREIGN KEY (service_id) REFERENCES public.services(id) ON DELETE SET NULL;
    END IF;
    
    -- Add used_parts-invoice foreign key if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.table_constraints WHERE constraint_name = 'fk_used_parts_invoice') THEN
        ALTER TABLE public.used_parts ADD CONSTRAINT fk_used_parts_invoice 
          FOREIGN KEY (invoice_id) REFERENCES public.invoices(id) ON DELETE CASCADE;
    END IF;
    
    -- Add used_parts-inventory foreign key if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.table_constraints WHERE constraint_name = 'fk_used_parts_inventory') THEN
        ALTER TABLE public.used_parts ADD CONSTRAINT fk_used_parts_inventory 
          FOREIGN KEY (item_id) REFERENCES public.inventory_items(id) ON DELETE SET NULL;
    END IF;
    
    -- Add comments-service foreign key if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.table_constraints WHERE constraint_name = 'fk_comments_service') THEN
        ALTER TABLE public.comments ADD CONSTRAINT fk_comments_service 
          FOREIGN KEY (service_id) REFERENCES public.services(id) ON DELETE CASCADE;
    END IF;
    
    -- Add comments-invoice foreign key if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.table_constraints WHERE constraint_name = 'fk_comments_invoice') THEN
        ALTER TABLE public.comments ADD CONSTRAINT fk_comments_invoice 
          FOREIGN KEY (invoice_id) REFERENCES public.invoices(id) ON DELETE CASCADE;
    END IF;
END $$;

-- Create expense_items table for invoice-specific expenses
CREATE TABLE IF NOT EXISTS public.expense_items (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  invoice_id UUID NOT NULL REFERENCES public.invoices(id) ON DELETE CASCADE,
  description TEXT NOT NULL,
  amount NUMERIC NOT NULL,
  category expense_category NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS on all tables (safe to run multiple times)
ALTER TABLE public.customers ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.services ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.invoices ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.inventory_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.expenses ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.used_parts ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.comments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.expense_items ENABLE ROW LEVEL SECURITY;

-- Drop existing RLS policies to avoid conflicts, then recreate them
DROP POLICY IF EXISTS "Users can manage their own customers" ON public.customers;
DROP POLICY IF EXISTS "Users can manage their own services" ON public.services;
DROP POLICY IF EXISTS "Users can manage their own invoices" ON public.invoices;
DROP POLICY IF EXISTS "Users can manage their own inventory" ON public.inventory_items;
DROP POLICY IF EXISTS "Users can manage their own expenses" ON public.expenses;
DROP POLICY IF EXISTS "Users can manage used parts for their invoices" ON public.used_parts;
DROP POLICY IF EXISTS "Users can manage comments for their content" ON public.comments;
DROP POLICY IF EXISTS "Users can manage expense items for their invoices" ON public.expense_items;

-- Create RLS policies
CREATE POLICY "Users can manage their own customers" ON public.customers
  FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can manage their own services" ON public.services
  FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can manage their own invoices" ON public.invoices
  FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can manage their own inventory" ON public.inventory_items
  FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can manage their own expenses" ON public.expenses
  FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can manage used parts for their invoices" ON public.used_parts
  FOR ALL USING (EXISTS (
    SELECT 1 FROM public.invoices 
    WHERE invoices.id = used_parts.invoice_id 
    AND invoices.user_id = auth.uid()
  ));

CREATE POLICY "Users can manage comments for their content" ON public.comments
  FOR ALL USING (
    (service_id IS NOT NULL AND EXISTS (
      SELECT 1 FROM public.services 
      WHERE services.id = comments.service_id 
      AND services.user_id = auth.uid()
    )) OR 
    (invoice_id IS NOT NULL AND EXISTS (
      SELECT 1 FROM public.invoices 
      WHERE invoices.id = comments.invoice_id 
      AND invoices.user_id = auth.uid()
    ))
  );

CREATE POLICY "Users can manage expense items for their invoices" ON public.expense_items
  FOR ALL USING (EXISTS (
    SELECT 1 FROM public.invoices 
    WHERE invoices.id = expense_items.invoice_id 
    AND invoices.user_id = auth.uid()
  ));

-- Create function to handle new user profile creation
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER SET search_path = ''
AS $$
BEGIN
  INSERT INTO public.profiles (id, first_name, last_name, email)
  VALUES (
    NEW.id,
    NEW.raw_user_meta_data ->> 'first_name',
    NEW.raw_user_meta_data ->> 'last_name',
    NEW.email
  );
  RETURN NEW;
END;
$$;

-- Create trigger for new user profile creation
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Apply updated_at triggers to relevant tables (drop first to avoid conflicts)
DROP TRIGGER IF EXISTS update_profiles_updated_at ON public.profiles;
DROP TRIGGER IF EXISTS update_customers_updated_at ON public.customers;
DROP TRIGGER IF EXISTS update_services_updated_at ON public.services;
DROP TRIGGER IF EXISTS update_invoices_updated_at ON public.invoices;
DROP TRIGGER IF EXISTS update_inventory_updated_at ON public.inventory_items;
DROP TRIGGER IF EXISTS update_expenses_updated_at ON public.expenses;

CREATE TRIGGER update_profiles_updated_at BEFORE UPDATE ON public.profiles
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_customers_updated_at BEFORE UPDATE ON public.customers
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_services_updated_at BEFORE UPDATE ON public.services
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_invoices_updated_at BEFORE UPDATE ON public.invoices
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_inventory_updated_at BEFORE UPDATE ON public.inventory_items
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_expenses_updated_at BEFORE UPDATE ON public.expenses
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();
