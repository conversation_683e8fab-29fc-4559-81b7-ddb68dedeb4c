
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { useWorkflowManager } from "@/hooks/useWorkflowManager";
import { Plus, FileText, Wrench, DollarSign } from "lucide-react";
import { toast } from "sonner";
import type { Jobsheet, Service } from "@/types";

interface QuickActionsProps {
  jobsheet?: Jobsheet;
  service?: Service;
  onServiceCreated?: (service: Service) => void;
  onInvoiceCreated?: (invoice: any) => void;
  className?: string;
}

export function QuickActions({ 
  jobsheet, 
  service, 
  onServiceCreated, 
  onInvoiceCreated,
  className 
}: QuickActionsProps) {
  const { createServiceFromJobsheet, createInvoiceFromService, isProcessing } = useWorkflowManager();

  const handleCreateService = async () => {
    if (!jobsheet) {
      toast.error('No jobsheet available to create service from');
      return;
    }

    const newService = await createServiceFromJobsheet(jobsheet);
    if (newService && onServiceCreated) {
      onServiceCreated(newService as Service);
    }
  };

  const handleCreateInvoice = async () => {
    if (!service) {
      toast.error('No service available to create invoice from');
      return;
    }

    if (service.status !== 'Completed') {
      toast.error('Service must be completed before creating an invoice');
      return;
    }

    const newInvoice = await createInvoiceFromService(service);
    if (newInvoice && onInvoiceCreated) {
      onInvoiceCreated(newInvoice);
    }
  };

  return (
    <Card className={className}>
      <CardHeader className="pb-3">
        <CardTitle className="text-sm font-medium">Quick Actions</CardTitle>
      </CardHeader>
      <CardContent className="space-y-2">
        {/* Create Service from Jobsheet */}
        {jobsheet && !service && (
          <Button
            onClick={handleCreateService}
            disabled={isProcessing || jobsheet.status !== 'In Progress'}
            className="w-full justify-start text-left"
            variant="outline"
            size="sm"
          >
            <Wrench className="w-4 h-4 mr-2" />
            Create Service
          </Button>
        )}

        {/* Create Invoice from Service */}
        {service && !onInvoiceCreated && (
          <Button
            onClick={handleCreateInvoice}
            disabled={isProcessing || service.status !== 'Completed'}
            className="w-full justify-start text-left"
            variant="outline"
            size="sm"
          >
            <FileText className="w-4 h-4 mr-2" />
            Create Invoice
          </Button>
        )}

        {/* Mark as Paid */}
        {service?.status === 'Completed' && (
          <Button
            className="w-full justify-start text-left"
            variant="outline"
            size="sm"
          >
            <DollarSign className="w-4 h-4 mr-2" />
            Mark as Paid
          </Button>
        )}

        {!jobsheet && !service && (
          <div className="text-sm text-gray-500 text-center py-4">
            No quick actions available
          </div>
        )}
      </CardContent>
    </Card>
  );
}
