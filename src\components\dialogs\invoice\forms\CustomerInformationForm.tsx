
import React, { useEffect, useState } from "react";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { ValidationMessage } from "@/components/ui/validation-message";
import { useAppData } from "@/contexts/AppDataContext";
import type { Invoice } from "../../../../types";

interface CustomerInformationFormProps {
  formData: Invoice;
  setFormData: (data: Invoice) => void;
  validationErrors?: { [key: string]: string };
}

export function CustomerInformationForm({ 
  formData, 
  setFormData, 
  validationErrors = {} 
}: CustomerInformationFormProps) {
  const { customers } = useAppData();
  const [suggestions, setSuggestions] = useState<string[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(false);

  const handleCustomerChange = (value: string) => {
    setFormData({ ...formData, customer: value });

    // Find matching customers and show suggestions
    if (value.length > 0) {
      const matches = customers
        .filter(customer => 
          customer.name.toLowerCase().includes(value.toLowerCase())
        )
        .map(customer => customer.name)
        .slice(0, 5);
      setSuggestions(matches);
      setShowSuggestions(matches.length > 0);
    } else {
      setShowSuggestions(false);
    }
  };

  const selectCustomer = (customerName: string) => {
    const selectedCustomer = customers.find(c => c.name === customerName);
    if (selectedCustomer) {
      setFormData({
        ...formData,
        customer: selectedCustomer.name,
        phone: selectedCustomer.phone,
        alternatePhone: selectedCustomer.alternatePhone || '',
        address: selectedCustomer.address,
        state: selectedCustomer.state || '',
        city: selectedCustomer.city || '',
        pincode: selectedCustomer.pincode || '',
        email: selectedCustomer.email || ''
      });
    }
    setShowSuggestions(false);
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
      <div className="relative">
        <Label htmlFor="customer">Customer Name *</Label>
        <Input
          id="customer"
          value={formData.customer}
          onChange={(e) => handleCustomerChange(e.target.value)}
          placeholder="Enter customer name"
          autoComplete="off"
          className={validationErrors.customer ? "border-red-500" : ""}
        />
        {validationErrors.customer && (
          <ValidationMessage message={validationErrors.customer} />
        )}
        {showSuggestions && (
          <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-40 overflow-y-auto">
            {suggestions.map((suggestion, index) => (
              <div
                key={index}
                className="px-3 py-2 cursor-pointer hover:bg-gray-100"
                onClick={() => selectCustomer(suggestion)}
              >
                {suggestion}
              </div>
            ))}
          </div>
        )}
      </div>

      <div>
        <Label htmlFor="phone">Phone Number *</Label>
        <Input
          id="phone"
          value={formData.phone}
          onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
          placeholder="Enter phone number"
          className={validationErrors.phone ? "border-red-500" : ""}
        />
        {validationErrors.phone && (
          <ValidationMessage message={validationErrors.phone} />
        )}
      </div>

      <div>
        <Label htmlFor="alternatePhone">Alternate Phone</Label>
        <Input
          id="alternatePhone"
          value={formData.alternatePhone}
          onChange={(e) => setFormData({ ...formData, alternatePhone: e.target.value })}
          placeholder="Enter alternate phone"
        />
      </div>

      <div>
        <Label htmlFor="email">Email Address</Label>
        <Input
          id="email"
          type="email"
          value={formData.email || ''}
          onChange={(e) => setFormData({ ...formData, email: e.target.value })}
          placeholder="Enter email address"
          className={validationErrors.email ? "border-red-500" : ""}
        />
        {validationErrors.email && (
          <ValidationMessage message={validationErrors.email} />
        )}
      </div>

      <div className="md:col-span-2">
        <Label htmlFor="address">Address</Label>
        <Input
          id="address"
          value={formData.address}
          onChange={(e) => setFormData({ ...formData, address: e.target.value })}
          placeholder="Enter full address"
        />
      </div>

      <div>
        <Label htmlFor="state">State</Label>
        <Input
          id="state"
          value={formData.state}
          onChange={(e) => setFormData({ ...formData, state: e.target.value })}
          placeholder="Enter state"
        />
      </div>

      <div>
        <Label htmlFor="city">City</Label>
        <Input
          id="city"
          value={formData.city}
          onChange={(e) => setFormData({ ...formData, city: e.target.value })}
          placeholder="Enter city"
        />
      </div>

      <div>
        <Label htmlFor="pincode">Pincode</Label>
        <Input
          id="pincode"
          value={formData.pincode}
          onChange={(e) => setFormData({ ...formData, pincode: e.target.value })}
          placeholder="Enter pincode"
        />
      </div>
    </div>
  );
}
