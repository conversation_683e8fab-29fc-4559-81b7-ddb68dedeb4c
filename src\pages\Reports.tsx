
import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Calendar, Download } from "lucide-react";
import { ReportTabs } from "@/components/reports/ReportTabs";
import { ServiceReport } from "@/components/reports/ServiceReport";
import { ProfitLossReport } from "@/components/reports/ProfitLossReport";
import { AdvancedReporting } from "@/components/analytics/AdvancedReporting";
import { useAppData } from "@/contexts/AppDataContext";
import { 
  ResponsiveContainer, 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  PieChart, 
  Pie, 
  Cell 
} from "recharts";
import type { Service } from "@/types/service";
import type { Invoice } from "@/types/invoice";
import type { Expense } from "@/types/expense";

export default function Reports() {
  const { services, customers, expenses, invoices } = useAppData();
  const [activeTab, setActiveTab] = useState("Advanced");

  // Format amounts for display (add ₹ symbol and handle numeric values)
  const formatAmount = (amount: number | string): string => {
    if (typeof amount === 'string') {
      // If it already has ₹, return as is, otherwise parse and format
      if (amount.includes('₹')) return amount;
      const numAmount = parseFloat(amount.replace(/[₹,]/g, '')) || 0;
      return `₹${numAmount.toLocaleString()}`;
    }
    return `₹${(amount || 0).toLocaleString()}`;
  };

  // Helper function to safely convert amount to number
  const getNumericAmount = (amount: string | number): number => {
    if (typeof amount === 'number') return amount;
    return parseFloat(amount.replace(/[₹,]/g, '') || '0');
  };

  // Calculate service data
  const servicesByStatus = [
    { name: 'Pending', value: services.filter(s => s.status === 'Pending').length, color: '#fbbf24' },
    { name: 'In Progress', value: services.filter(s => s.status === 'In Progress').length, color: '#3b82f6' },
    { name: 'Completed', value: services.filter(s => s.status === 'Completed').length, color: '#10b981' },
  ];

  // Calculate monthly service performance
  const servicePerformance = [
    { month: 'Jan', completed: 25, pending: 8, revenue: 85000 },
    { month: 'Feb', completed: 30, pending: 5, revenue: 92000 },
    { month: 'Mar', completed: 28, pending: 12, revenue: 88000 },
    { month: 'Apr', completed: 35, pending: 7, revenue: 105000 },
    { month: 'May', completed: 32, pending: 9, revenue: 98000 },
    { month: 'Jun', completed: 40, pending: 6, revenue: 120000 },
  ];

  // Transform services to match type requirements - use fallback for missing properties
  const transformedServices: Service[] = services.map(service => ({
    ...service,
    customer: service.customer || '',
    date: new Date().toISOString().split('T')[0], // Use current date as fallback
    status: (service.status || 'Pending') as Service['status'],
    service_type: (service.service_type || 'Repair') as Service['service_type'],
    priority: (service.priority || 'Medium') as Service['priority']
  }));

  // Transform invoices to match type requirements - use fallback for missing properties
  const transformedInvoices: Invoice[] = invoices.map(invoice => ({
    ...invoice,
    customer: invoice.customer || '',
    date: invoice.date || new Date().toISOString().split('T')[0],
    status: (invoice.status || 'Pending') as Invoice['status'],
    amount: formatAmount(invoice.amount), // Convert numeric to formatted string
    inspection_fee: invoice.inspection_fee || '500',
    gst: invoice.gst || '0',
    estimated_amount: invoice.estimated_amount || undefined
  }));

  // Transform expenses to match type requirements - handle numeric amounts properly
  const transformedExpenses: Expense[] = expenses.map(expense => ({
    ...expense,
    category: expense.category as Expense['category'],
    amount: formatAmount(expense.amount) // Convert numeric to formatted string
  }));

  // Calculate customer data - handle numeric amounts
  const totalRevenue = invoices.reduce((sum, invoice) => 
    sum + getNumericAmount(invoice.amount), 0
  );
  
  const topCustomers = customers
    .map(customer => ({
      name: customer.name,
      services: transformedServices.filter(s => s.customer === customer.name).length,
      revenue: invoices.filter(i => i.customer === customer.name)
        .reduce((sum, invoice) => sum + getNumericAmount(invoice.amount), 0)
    }))
    .sort((a, b) => b.revenue - a.revenue)
    .slice(0, 5);

  // Calculate profit and loss - handle numeric amounts
  const totalExpenses = expenses.reduce((sum, expense) => 
    sum + getNumericAmount(expense.amount), 0
  );
  const profit = totalRevenue - totalExpenses;

  const profitLossData = [
    { month: 'Jan', revenue: 120000, expenses: 45000, profit: 75000 },
    { month: 'Feb', revenue: 135000, expenses: 48000, profit: 87000 },
    { month: 'Mar', revenue: 128000, expenses: 52000, profit: 76000 },
    { month: 'Apr', revenue: 145000, expenses: 50000, profit: 95000 },
    { month: 'May', revenue: 158000, expenses: 55000, profit: 103000 },
    { month: 'Jun', revenue: 172000, expenses: 58000, profit: 114000 },
  ];

  // Calculate expenses by category - handle numeric amounts
  const expensesByCategory = Array.from(new Set(transformedExpenses.map(expense => expense.category)))
    .map(category => ({
      name: category,
      value: expenses.filter(expense => expense.category === category)
        .reduce((sum, expense) => sum + getNumericAmount(expense.amount), 0),
      color: `hsl(${Math.random() * 360}, 70%, 50%)`
    }));

  const renderContent = () => {
    switch (activeTab) {
      case "Advanced":
        return (
          <AdvancedReporting 
            services={transformedServices}
            invoices={transformedInvoices}
            customers={customers}
            inventory={[]}
            expenses={transformedExpenses}
          />
        );
      case "Service":
        return <ServiceReport services={transformedServices} />;
      case "Customer":
        return (
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Top Customers by Revenue</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart data={topCustomers} layout="horizontal">
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis type="number" />
                    <YAxis dataKey="name" type="category" width={100} />
                    <Tooltip />
                    <Bar dataKey="revenue" fill="#f97316" />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>
        );
      case "Profit & Loss":
        return <ProfitLossReport invoices={transformedInvoices} expenses={transformedExpenses} />;
      case "Expenses":
        return (
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Expenses by Category</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <PieChart>
                    <Pie
                      data={expensesByCategory}
                      cx="50%"
                      cy="50%"
                      innerRadius={60}
                      outerRadius={120}
                      paddingAngle={5}
                      dataKey="value"
                    >
                      {expensesByCategory.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </PieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Monthly Expense Breakdown</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart data={profitLossData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis />
                    <Tooltip />
                    <Bar dataKey="expenses" fill="#ef4444" />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>
        );
      default:
        return (
          <AdvancedReporting 
            services={transformedServices}
            invoices={transformedInvoices}
            customers={customers}
            inventory={[]}
            expenses={transformedExpenses}
          />
        );
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-gray-900">Reports & Analytics</h1>
        <div className="flex items-center space-x-4">
          <Button variant="outline">
            <Calendar className="w-4 h-4 mr-2" />
            This Month
          </Button>
          <Button variant="outline">
            <Download className="w-4 h-4 mr-2" />
            Export Report
          </Button>
        </div>
      </div>

      <p className="text-gray-600">Comprehensive business analytics and reporting</p>

      <ReportTabs activeTab={activeTab} setActiveTab={setActiveTab} />

      {renderContent()}
    </div>
  );
}
