
-- Create comments table to track all activity (if not exists)
CREATE TABLE IF NOT EXISTS public.comments (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  entity_id UUID NOT NULL,
  entity_type TEXT NOT NULL CHECK (entity_type IN ('jobsheet', 'service', 'invoice', 'customer')),
  text TEXT NOT NULL,
  author TEXT NOT NULL,
  comment_type TEXT NOT NULL CHECK (comment_type IN ('edit', 'customer', 'internal', 'status_change', 'system')),
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  timestamp TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Create used_parts table to track parts used in invoices (if not exists)
CREATE TABLE IF NOT EXISTS public.used_parts (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  invoice_id UUID REFERENCES public.invoices(id) ON DELETE CASCADE,
  item_id TEXT,
  item_name TEXT NOT NULL,
  quantity INTEGER NOT NULL DEFAULT 1,
  price NUMERIC NOT NULL DEFAULT 0,
  manufacturer TEXT,
  model TEXT,
  serial_no TEXT,
  is_custom BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Add foreign key relationships that were missing (if not exists)
DO $$ 
BEGIN
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'services' AND column_name = 'jobsheet_id') THEN
    ALTER TABLE public.services ADD COLUMN jobsheet_id UUID REFERENCES public.jobsheets(id) ON DELETE SET NULL;
  END IF;
END $$;

DO $$ 
BEGIN
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'invoices' AND column_name = 'service_id') THEN
    ALTER TABLE public.invoices ADD COLUMN service_id UUID REFERENCES public.services(id) ON DELETE SET NULL;
  END IF;
END $$;

DO $$ 
BEGIN
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'expenses' AND column_name = 'invoice_id') THEN
    ALTER TABLE public.expenses ADD COLUMN invoice_id UUID REFERENCES public.invoices(id) ON DELETE SET NULL;
  END IF;
END $$;

-- Create trigger function to automatically create comments for jobsheet operations
CREATE OR REPLACE FUNCTION public.log_jobsheet_activity()
RETURNS TRIGGER AS $$
BEGIN
  IF TG_OP = 'INSERT' THEN
    INSERT INTO public.comments (entity_id, entity_type, text, author, comment_type)
    VALUES (
      NEW.id,
      'jobsheet',
      'Jobsheet created for ' || NEW.device_type || ' - ' || NEW.device_brand || ' ' || NEW.device_model,
      COALESCE(NEW.received_by, 'System'),
      'system'
    );
    RETURN NEW;
  ELSIF TG_OP = 'UPDATE' THEN
    -- Log status changes
    IF OLD.status != NEW.status THEN
      INSERT INTO public.comments (entity_id, entity_type, text, author, comment_type)
      VALUES (
        NEW.id,
        'jobsheet',
        'Status changed from ' || OLD.status || ' to ' || NEW.status,
        COALESCE(NEW.technician_assigned, 'System'),
        'status_change'
      );
    END IF;
    
    -- Log technician assignment
    IF COALESCE(OLD.technician_assigned, '') != COALESCE(NEW.technician_assigned, '') THEN
      INSERT INTO public.comments (entity_id, entity_type, text, author, comment_type)
      VALUES (
        NEW.id,
        'jobsheet',
        'Technician assigned: ' || COALESCE(NEW.technician_assigned, 'Unassigned'),
        'System',
        'system'
      );
    END IF;
    
    RETURN NEW;
  END IF;
  RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Create trigger function to automatically create services from completed jobsheets
CREATE OR REPLACE FUNCTION public.auto_create_service_from_jobsheet()
RETURNS TRIGGER AS $$
DECLARE
  service_id UUID;
BEGIN
  -- Only create service when jobsheet status changes to 'In Progress' and no service exists
  IF NEW.status = 'In Progress' AND OLD.status != 'In Progress' THEN
    -- Check if service already exists for this jobsheet
    SELECT id INTO service_id FROM public.services WHERE jobsheet_id = NEW.id;
    
    IF service_id IS NULL THEN
      -- Create new service
      INSERT INTO public.services (
        customer,
        phone,
        device,
        issue,
        status,
        service_type,
        priority,
        technician,
        estimated_completion,
        cost,
        customer_id,
        jobsheet_id,
        user_id
      ) VALUES (
        NEW.customer_name,
        NEW.phone,
        NEW.device_type || ' - ' || NEW.device_brand || ' ' || NEW.device_model,
        NEW.reported_issues,
        'In Progress',
        'Repair',
        NEW.priority,
        NEW.technician_assigned,
        NEW.expected_delivery,
        COALESCE(NEW.estimated_cost, 0),
        NEW.customer_id,
        NEW.id,
        NEW.user_id
      ) RETURNING id INTO service_id;
      
      -- Log the service creation
      INSERT INTO public.comments (entity_id, entity_type, text, author, comment_type)
      VALUES (
        service_id,
        'service',
        'Service automatically created from jobsheet #' || NEW.id,
        'System',
        'system'
      );
    END IF;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger function to sync service status with jobsheet
CREATE OR REPLACE FUNCTION public.sync_service_jobsheet_status()
RETURNS TRIGGER AS $$
BEGIN
  -- Update jobsheet status when service is completed
  IF NEW.status = 'Completed' AND OLD.status != 'Completed' THEN
    UPDATE public.jobsheets 
    SET status = 'Completed'
    WHERE id = NEW.jobsheet_id;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers
DROP TRIGGER IF EXISTS trigger_log_jobsheet_activity ON public.jobsheets;
CREATE TRIGGER trigger_log_jobsheet_activity
  AFTER INSERT OR UPDATE ON public.jobsheets
  FOR EACH ROW EXECUTE FUNCTION public.log_jobsheet_activity();

DROP TRIGGER IF EXISTS trigger_auto_create_service ON public.jobsheets;
CREATE TRIGGER trigger_auto_create_service
  AFTER UPDATE ON public.jobsheets
  FOR EACH ROW EXECUTE FUNCTION public.auto_create_service_from_jobsheet();

DROP TRIGGER IF EXISTS trigger_sync_service_status ON public.services;
CREATE TRIGGER trigger_sync_service_status
  AFTER UPDATE ON public.services
  FOR EACH ROW EXECUTE FUNCTION public.sync_service_jobsheet_status();

-- Enable RLS on new tables (only if not already enabled)
DO $$ 
BEGIN
  IF NOT EXISTS (SELECT 1 FROM pg_tables WHERE schemaname = 'public' AND tablename = 'comments' AND rowsecurity = true) THEN
    ALTER TABLE public.comments ENABLE ROW LEVEL SECURITY;
  END IF;
END $$;

DO $$ 
BEGIN
  IF NOT EXISTS (SELECT 1 FROM pg_tables WHERE schemaname = 'public' AND tablename = 'used_parts' AND rowsecurity = true) THEN
    ALTER TABLE public.used_parts ENABLE ROW LEVEL SECURITY;
  END IF;
END $$;

-- Drop and recreate RLS policies to avoid conflicts
DROP POLICY IF EXISTS "Users can view comments for their entities" ON public.comments;
DROP POLICY IF EXISTS "Users can create comments for their entities" ON public.comments;
DROP POLICY IF EXISTS "Users can view their used parts" ON public.used_parts;
DROP POLICY IF EXISTS "Users can manage their used parts" ON public.used_parts;

-- Create RLS policies for comments (users can see comments for their entities)
CREATE POLICY "Users can view comments for their entities" ON public.comments
FOR SELECT USING (
  EXISTS (
    SELECT 1 FROM public.jobsheets j WHERE j.id = entity_id AND j.user_id::text = 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11' AND entity_type = 'jobsheet'
  ) OR
  EXISTS (
    SELECT 1 FROM public.services s WHERE s.id = entity_id AND s.user_id::text = 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11' AND entity_type = 'service'
  ) OR
  EXISTS (
    SELECT 1 FROM public.invoices i WHERE i.id = entity_id AND i.user_id::text = 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11' AND entity_type = 'invoice'
  ) OR
  EXISTS (
    SELECT 1 FROM public.customers c WHERE c.id = entity_id AND c.user_id::text = 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11' AND entity_type = 'customer'
  )
);

CREATE POLICY "Users can create comments for their entities" ON public.comments
FOR INSERT WITH CHECK (
  EXISTS (
    SELECT 1 FROM public.jobsheets j WHERE j.id = entity_id AND j.user_id::text = 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11' AND entity_type = 'jobsheet'
  ) OR
  EXISTS (
    SELECT 1 FROM public.services s WHERE s.id = entity_id AND s.user_id::text = 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11' AND entity_type = 'service'
  ) OR
  EXISTS (
    SELECT 1 FROM public.invoices i WHERE i.id = entity_id AND i.user_id::text = 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11' AND entity_type = 'invoice'
  ) OR
  EXISTS (
    SELECT 1 FROM public.customers c WHERE c.id = entity_id AND c.user_id::text = 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11' AND entity_type = 'customer'
  )
);

-- Create RLS policies for used_parts
CREATE POLICY "Users can view their used parts" ON public.used_parts
FOR SELECT USING (
  EXISTS (
    SELECT 1 FROM public.invoices i WHERE i.id = invoice_id AND i.user_id::text = 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11'
  )
);

CREATE POLICY "Users can manage their used parts" ON public.used_parts
FOR ALL USING (
  EXISTS (
    SELECT 1 FROM public.invoices i WHERE i.id = invoice_id AND i.user_id::text = 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11'
  )
);
