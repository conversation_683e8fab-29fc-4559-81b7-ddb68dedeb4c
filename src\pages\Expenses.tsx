
import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Plus } from "lucide-react";
import { ExpenseDialog } from "@/components/dialogs/ExpenseDialog";
import { ExpenseSummaryCards } from "@/components/expenses/ExpenseSummaryCards";
import { ExpenseTable } from "@/components/expenses/ExpenseTable";
import { ExpenseChart } from "@/components/expenses/ExpenseChart";
import { useToast } from "@/hooks/use-toast";
import { useAppData } from "@/contexts/AppDataContext";
import type { Expense } from "@/types/expense";

export default function Expenses() {
  const { expenses, addExpense, updateExpense, deleteExpense } = useAppData();
  const [searchTerm, setSearchTerm] = useState("");
  const [categoryFilter, setCategoryFilter] = useState("All Categories");
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingExpense, setEditingExpense] = useState(undefined);
  const { toast } = useToast();

  const categories: string[] = ["All Categories", ...Array.from(new Set(expenses.map(e => e.category)))];

  // Format amount for display and calculations
  const formatAmount = (amount: number | string): string => {
    if (typeof amount === 'string') {
      if (amount.includes('₹')) return amount;
      const numAmount = parseFloat(amount.replace(/[₹,]/g, '')) || 0;
      return `₹${numAmount.toLocaleString()}`;
    }
    return `₹${(amount || 0).toLocaleString()}`;
  };

  const getNumericAmount = (amount: number | string): number => {
    if (typeof amount === 'number') return amount;
    return parseFloat(amount.replace(/[₹,]/g, '')) || 0;
  };

  const categoryData: { name: string; value: number; color: string; }[] = categories.slice(1).map((category, index) => {
    const categoryExpenses = expenses.filter(e => e.category === category);
    const total = categoryExpenses.reduce((sum, e) => sum + getNumericAmount(e.amount), 0);
    const colors = ['#f97316', '#fb923c', '#fdba74', '#fed7aa', '#ffedd5', '#fff7ed'];
    return {
      name: category,
      value: total,
      color: colors[index % colors.length]
    };
  });

  const handleAddExpense = () => {
    setEditingExpense(undefined);
    setIsDialogOpen(true);
  };

  const handleEditExpense = (expense: any) => {
    setEditingExpense(expense);
    setIsDialogOpen(true);
  };

  const handleDeleteExpense = (expenseId: string) => {
    deleteExpense(expenseId);
    toast({
      title: "Expense Deleted",
      description: "Expense has been successfully deleted.",
    });
  };

  const handleSaveExpense = (expenseData: any) => {
    if (editingExpense) {
      updateExpense(editingExpense.id, expenseData);
      toast({
        title: "Expense Updated",
        description: "Expense has been successfully updated.",
      });
    } else {
      addExpense(expenseData);
      toast({
        title: "Expense Added",
        description: "New expense has been successfully added.",
      });
    }
  };

  // Transform expenses to match the expected type structure with proper amount formatting
  const transformedExpenses: Expense[] = expenses.map(expense => ({
    ...expense,
    date: expense.date || new Date().toISOString().split('T')[0],
    vendor: expense.vendor || 'Unknown',
    category: expense.category as Expense['category'],
    amount: formatAmount(expense.amount) // Ensure consistent formatting
  }));

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-gray-900">Expense Tracker</h1>
        <Button onClick={handleAddExpense} className="bg-orange-500 hover:bg-orange-600 text-white">
          <Plus className="w-4 h-4 mr-2" />
          Add Expense
        </Button>
      </div>

      <ExpenseSummaryCards expenses={transformedExpenses} categoryData={categoryData} />

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2">
          <ExpenseTable
            expenses={transformedExpenses}
            searchTerm={searchTerm}
            setSearchTerm={setSearchTerm}
            categoryFilter={categoryFilter}
            setCategoryFilter={setCategoryFilter}
            categories={categories}
            onEditExpense={handleEditExpense}
            onDeleteExpense={handleDeleteExpense}
          />
        </div>

        <div>
          <ExpenseChart categoryData={categoryData} />
        </div>
      </div>

      <ExpenseDialog
        isOpen={isDialogOpen}
        onClose={() => setIsDialogOpen(false)}
        expense={editingExpense}
        onSave={handleSaveExpense}
      />
    </div>
  );
}
