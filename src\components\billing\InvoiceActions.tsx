
import { useToast } from "@/hooks/use-toast";

export function useInvoiceActions() {
  const { toast } = useToast();

  const handleMailInvoice = (invoice: any) => {
    const subject = `Invoice ${invoice?.id} - ${invoice?.customer}`;
    const body = `Dear ${invoice?.customer},

Please find attached your invoice?.

Invoice ID: ${invoice?.id}
Device: ${invoice?.device || 'N/A'}
Amount: ${invoice?.amount}

Thank you for your business!

Best regards,
Service Team`;
    
    const mailtoLink = `mailto:?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;
    window.open(mailtoLink);
    
    toast({
      title: "Email Client Opened",
      description: "Your default email client has been opened with the invoice details.",
    });
  };

  const handleDownloadInvoice = (invoice: any) => {
//     const invoiceText = `
// INVOICE: ${invoice?.id}
// Customer: ${invoice?.customer}
// Date: ${invoice?.date}
// Device: ${invoice?.device || 'N/A'}
// Issue: ${invoice?.issue || 'N/A'}
// Amount: ${invoice?.amount}
// Status: ${invoice?.status}

// Parts Used:
// ${invoice?.usedParts?.map((part: any) => `- ${part.name} x${part.quantity}`).join('\n') || 'None'}

// Additional Expenses:
// ${invoice?.expenses?.map((expense: any) => `- ${expense.description}: ${expense.amount}`).join('\n') || 'None'}
//     `;
    
//     const blob = new Blob([invoiceText], { type: 'text/plain' });
//     const url = URL.createObjectURL(blob);
//     const a = document.createElement('a');
//     a.href = url;
//     a.download = `Invoice-${invoice?.id}.txt`;
//     document.body.appendChild(a);
//     a.click();
//     document.body.removeChild(a);
//     URL.revokeObjectURL(url);
    
    toast({
      title: "Invoice Downloaded",
      description: "Invoice has been downloaded successfully.",
    });
    window.open(`http://localhost:8081/?print=true&customerName=${invoice?.customer ?? "" }&requestNo=${invoice?.id ?? "" }&requestDate=${invoice?.date ?? "" }&customerNo=${invoice?.phone ?? "" }&altContactNo=${invoice?.alternatePhone ?? "" }&customerAddress=${invoice?.address && invoice?.city && invoice?.state && invoice?.pincode ? invoice?.address+", "+invoice?.city+","+invoice?.state+" - "+invoice?.pincode : "" }&deviceType=${invoice?.deviceType ? invoice?.deviceType.replace(/\//g, ' / ') : "" }&otherDeviceType=${invoice?.customDeviceName ?? "" }&manufacturer=${invoice?.manufacturer ?? invoice?.device ?? "" }&model=${invoice?.model ?? "" }&serialNo=${invoice?.serialNo ?? "" }&defectDescription=${invoice?.issue ?? "" }&password=${invoice?.password ?? "" }&accessories=${JSON.stringify(invoice?.accessories) ?? "" }&serviceType=${invoice?.billableWarranty ?? "" }&inspectionCharge=${invoice?.inspectionFee ?? "" }&estimatedCost=${invoice?.estimatedAmount ?? "" }&finalCost=${invoice?.amount ?? "" }&expectedDelivery=${invoice?.expectedDelivery ?? ""}`);
  };

  return {
    handleMailInvoice,
    handleDownloadInvoice
  };
}
