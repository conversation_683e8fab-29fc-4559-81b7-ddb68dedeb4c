
import { Button } from "@/components/ui/button";

interface InvoiceFormActionsProps {
  invoice?: any;
  onClose: () => void;
}

export function InvoiceFormActions({ invoice, onClose }: InvoiceFormActionsProps) {
  return (
    <div className="flex justify-end space-x-2 pt-4 border-t">
      <Button type="button" variant="outline" onClick={onClose}>
        Cancel
      </Button>
      <Button type="submit" className="bg-orange-500 hover:bg-orange-600">
        {invoice ? 'Update' : 'Create'} Final Invoice
      </Button>
    </div>
  );
}
