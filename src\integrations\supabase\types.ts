export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      comments: {
        Row: {
          author: string
          comment_type: string
          created_at: string
          entity_id: string
          entity_type: string
          id: string
          text: string
          timestamp: string
        }
        Insert: {
          author: string
          comment_type: string
          created_at?: string
          entity_id: string
          entity_type: string
          id?: string
          text: string
          timestamp?: string
        }
        Update: {
          author?: string
          comment_type?: string
          created_at?: string
          entity_id?: string
          entity_type?: string
          id?: string
          text?: string
          timestamp?: string
        }
        Relationships: []
      }
      customers: {
        Row: {
          address: string | null
          alternate_phone: string | null
          city: string | null
          created_at: string
          credit_limit: number | null
          customer_type: string | null
          email: string | null
          id: string
          last_service_date: string | null
          name: string
          notes: string | null
          payment_terms: string | null
          phone: string
          pincode: string | null
          preferred_contact: string | null
          state: string | null
          tax_id: string | null
          total_spent: number | null
          updated_at: string
          user_id: string
        }
        Insert: {
          address?: string | null
          alternate_phone?: string | null
          city?: string | null
          created_at?: string
          credit_limit?: number | null
          customer_type?: string | null
          email?: string | null
          id?: string
          last_service_date?: string | null
          name: string
          notes?: string | null
          payment_terms?: string | null
          phone: string
          pincode?: string | null
          preferred_contact?: string | null
          state?: string | null
          tax_id?: string | null
          total_spent?: number | null
          updated_at?: string
          user_id: string
        }
        Update: {
          address?: string | null
          alternate_phone?: string | null
          city?: string | null
          created_at?: string
          credit_limit?: number | null
          customer_type?: string | null
          email?: string | null
          id?: string
          last_service_date?: string | null
          name?: string
          notes?: string | null
          payment_terms?: string | null
          phone?: string
          pincode?: string | null
          preferred_contact?: string | null
          state?: string | null
          tax_id?: string | null
          total_spent?: number | null
          updated_at?: string
          user_id?: string
        }
        Relationships: []
      }
      expenses: {
        Row: {
          amount: number
          approved_by: string | null
          category: string
          created_at: string
          date: string
          department: string | null
          description: string
          id: string
          invoice_id: string | null
          payment_method: string | null
          receipt: string | null
          reference_number: string | null
          tax_amount: number | null
          updated_at: string
          user_id: string
          vendor: string
        }
        Insert: {
          amount: number
          approved_by?: string | null
          category: string
          created_at?: string
          date?: string
          department?: string | null
          description: string
          id?: string
          invoice_id?: string | null
          payment_method?: string | null
          receipt?: string | null
          reference_number?: string | null
          tax_amount?: number | null
          updated_at?: string
          user_id: string
          vendor: string
        }
        Update: {
          amount?: number
          approved_by?: string | null
          category?: string
          created_at?: string
          date?: string
          department?: string | null
          description?: string
          id?: string
          invoice_id?: string | null
          payment_method?: string | null
          receipt?: string | null
          reference_number?: string | null
          tax_amount?: number | null
          updated_at?: string
          user_id?: string
          vendor?: string
        }
        Relationships: [
          {
            foreignKeyName: "expenses_invoice_id_fkey"
            columns: ["invoice_id"]
            isOneToOne: false
            referencedRelation: "invoices"
            referencedColumns: ["id"]
          },
        ]
      }
      invoices: {
        Row: {
          address: string | null
          alternate_phone: string | null
          amount: number
          billable_warranty: string | null
          city: string | null
          created_at: string
          custom_device_name: string | null
          customer: string
          customer_id: string | null
          date: string
          device: string | null
          device_type: string | null
          discount_amount: number | null
          due_date: string | null
          email: string | null
          estimated_amount: number | null
          expected_delivery: string | null
          gst: number | null
          id: string
          inspection_fee: number | null
          issue: string | null
          notes: string | null
          payment_method: string | null
          payment_terms: string | null
          phone: string | null
          pincode: string | null
          remarks: string | null
          service_id: string | null
          show_remarks: boolean | null
          state: string | null
          status: string | null
          tax_amount: number | null
          updated_at: string
          user_id: string
        }
        Insert: {
          address?: string | null
          alternate_phone?: string | null
          amount: number
          billable_warranty?: string | null
          city?: string | null
          created_at?: string
          custom_device_name?: string | null
          customer: string
          customer_id?: string | null
          date?: string
          device?: string | null
          device_type?: string | null
          discount_amount?: number | null
          due_date?: string | null
          email?: string | null
          estimated_amount?: number | null
          expected_delivery?: string | null
          gst?: number | null
          id?: string
          inspection_fee?: number | null
          issue?: string | null
          notes?: string | null
          payment_method?: string | null
          payment_terms?: string | null
          phone?: string | null
          pincode?: string | null
          remarks?: string | null
          service_id?: string | null
          show_remarks?: boolean | null
          state?: string | null
          status?: string | null
          tax_amount?: number | null
          updated_at?: string
          user_id: string
        }
        Update: {
          address?: string | null
          alternate_phone?: string | null
          amount?: number
          billable_warranty?: string | null
          city?: string | null
          created_at?: string
          custom_device_name?: string | null
          customer?: string
          customer_id?: string | null
          date?: string
          device?: string | null
          device_type?: string | null
          discount_amount?: number | null
          due_date?: string | null
          email?: string | null
          estimated_amount?: number | null
          expected_delivery?: string | null
          gst?: number | null
          id?: string
          inspection_fee?: number | null
          issue?: string | null
          notes?: string | null
          payment_method?: string | null
          payment_terms?: string | null
          phone?: string | null
          pincode?: string | null
          remarks?: string | null
          service_id?: string | null
          show_remarks?: boolean | null
          state?: string | null
          status?: string | null
          tax_amount?: number | null
          updated_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "invoices_customer_id_fkey"
            columns: ["customer_id"]
            isOneToOne: false
            referencedRelation: "customers"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "invoices_service_id_fkey"
            columns: ["service_id"]
            isOneToOne: false
            referencedRelation: "services"
            referencedColumns: ["id"]
          },
        ]
      }
      jobsheets: {
        Row: {
          accessories_received: string | null
          address: string | null
          advance_payment: number | null
          alternate_phone: string | null
          created_at: string
          customer_id: string | null
          customer_name: string
          customer_signature: boolean | null
          date: string
          device_brand: string
          device_model: string
          device_type: string
          email: string | null
          estimated_cost: number | null
          expected_delivery: string | null
          id: string
          password: string | null
          phone: string
          physical_condition: string
          priority: string
          received_by: string
          reported_issues: string
          serial_number: string | null
          special_instructions: string | null
          status: string
          technician_assigned: string | null
          terms_accepted: boolean | null
          updated_at: string
          user_id: string
          warranty_terms: string | null
        }
        Insert: {
          accessories_received?: string | null
          address?: string | null
          advance_payment?: number | null
          alternate_phone?: string | null
          created_at?: string
          customer_id?: string | null
          customer_name: string
          customer_signature?: boolean | null
          date?: string
          device_brand: string
          device_model: string
          device_type: string
          email?: string | null
          estimated_cost?: number | null
          expected_delivery?: string | null
          id?: string
          password?: string | null
          phone: string
          physical_condition: string
          priority: string
          received_by: string
          reported_issues: string
          serial_number?: string | null
          special_instructions?: string | null
          status: string
          technician_assigned?: string | null
          terms_accepted?: boolean | null
          updated_at?: string
          user_id: string
          warranty_terms?: string | null
        }
        Update: {
          accessories_received?: string | null
          address?: string | null
          advance_payment?: number | null
          alternate_phone?: string | null
          created_at?: string
          customer_id?: string | null
          customer_name?: string
          customer_signature?: boolean | null
          date?: string
          device_brand?: string
          device_model?: string
          device_type?: string
          email?: string | null
          estimated_cost?: number | null
          expected_delivery?: string | null
          id?: string
          password?: string | null
          phone?: string
          physical_condition?: string
          priority?: string
          received_by?: string
          reported_issues?: string
          serial_number?: string | null
          special_instructions?: string | null
          status?: string
          technician_assigned?: string | null
          terms_accepted?: boolean | null
          updated_at?: string
          user_id?: string
          warranty_terms?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "jobsheets_customer_id_fkey"
            columns: ["customer_id"]
            isOneToOne: false
            referencedRelation: "customers"
            referencedColumns: ["id"]
          },
        ]
      }
      services: {
        Row: {
          actual_completion: string | null
          cost: number | null
          created_at: string
          customer: string
          customer_id: string | null
          device: string
          estimated_completion: string | null
          id: string
          issue: string
          jobsheet_id: string | null
          labor_cost: number | null
          parts_cost: number | null
          phone: string
          priority: string | null
          service_type: string | null
          status: string
          technician: string | null
          updated_at: string
          user_id: string
          warranty_period: number | null
        }
        Insert: {
          actual_completion?: string | null
          cost?: number | null
          created_at?: string
          customer: string
          customer_id?: string | null
          device: string
          estimated_completion?: string | null
          id?: string
          issue: string
          jobsheet_id?: string | null
          labor_cost?: number | null
          parts_cost?: number | null
          phone: string
          priority?: string | null
          service_type?: string | null
          status: string
          technician?: string | null
          updated_at?: string
          user_id: string
          warranty_period?: number | null
        }
        Update: {
          actual_completion?: string | null
          cost?: number | null
          created_at?: string
          customer?: string
          customer_id?: string | null
          device?: string
          estimated_completion?: string | null
          id?: string
          issue?: string
          jobsheet_id?: string | null
          labor_cost?: number | null
          parts_cost?: number | null
          phone?: string
          priority?: string | null
          service_type?: string | null
          status?: string
          technician?: string | null
          updated_at?: string
          user_id?: string
          warranty_period?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "services_customer_id_fkey"
            columns: ["customer_id"]
            isOneToOne: false
            referencedRelation: "customers"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "services_jobsheet_id_fkey"
            columns: ["jobsheet_id"]
            isOneToOne: false
            referencedRelation: "jobsheets"
            referencedColumns: ["id"]
          },
        ]
      }
      used_parts: {
        Row: {
          created_at: string
          id: string
          invoice_id: string | null
          is_custom: boolean | null
          item_id: string | null
          item_name: string
          manufacturer: string | null
          model: string | null
          price: number
          quantity: number
          serial_no: string | null
        }
        Insert: {
          created_at?: string
          id?: string
          invoice_id?: string | null
          is_custom?: boolean | null
          item_id?: string | null
          item_name: string
          manufacturer?: string | null
          model?: string | null
          price: number
          quantity: number
          serial_no?: string | null
        }
        Update: {
          created_at?: string
          id?: string
          invoice_id?: string | null
          is_custom?: boolean | null
          item_id?: string | null
          item_name?: string
          manufacturer?: string | null
          model?: string | null
          price?: number
          quantity?: number
          serial_no?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "used_parts_invoice_id_fkey"
            columns: ["invoice_id"]
            isOneToOne: false
            referencedRelation: "invoices"
            referencedColumns: ["id"]
          },
        ]
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      calculate_expected_delivery: {
        Args: { start_date?: string }
        Returns: string
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DefaultSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {},
  },
} as const
