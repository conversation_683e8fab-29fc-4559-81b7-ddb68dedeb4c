
import { Comment } from './common';

export interface Service {
  id: string;
  customer: string;
  customer_name?: string;
  phone: string;
  device: string;
  issue: string;
  status: 'Pending' | 'In Progress' | 'Completed' | 'On Hold' | 'Cancelled';
  service_type?: 'Repair' | 'Maintenance' | 'Installation' | 'Consultation' | 'Upgrade';
  priority?: 'Low' | 'Medium' | 'High' | 'Urgent';
  technician?: string;
  estimated_completion?: string;
  actual_completion?: string;
  warranty_period?: number;
  cost?: number;
  parts_cost?: number;
  labor_cost?: number;
  customer_id?: string;
  jobsheet_id?: string;
  user_id: string;
  comments?: Comment[];
}
