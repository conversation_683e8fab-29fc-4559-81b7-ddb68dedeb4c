
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Loader2 } from 'lucide-react';
import { toast } from 'sonner';

export function SampleDataSeeder() {
  const [isSeeding, setIsSeeding] = useState(false);

  const seedSampleData = async () => {
    setIsSeeding(true);
    try {
      // This will be updated with new business logic
      toast.success('Ready for new business logic implementation');
    } catch (error) {
      console.error('Error:', error);
      toast.error('An error occurred');
    } finally {
      setIsSeeding(false);
    }
  };

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="text-center">Business Logic Removed</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="text-center text-gray-600">
          <p>All existing business logic has been cleared.</p>
          <p>Ready for new business logic and data flow implementation.</p>
        </div>
        
        <div className="bg-blue-50 p-4 rounded-lg">
          <h3 className="font-semibold text-blue-900 mb-2">System Status:</h3>
          <ul className="text-sm text-blue-800 space-y-1">
            <li>• Database connections verified</li>
            <li>• All tables properly connected to Supabase</li>
            <li>• Ready for new business logic</li>
          </ul>
        </div>

        <Button 
          onClick={seedSampleData} 
          disabled={isSeeding}
          className="w-full bg-orange-500 hover:bg-orange-600"
          size="lg"
        >
          {isSeeding ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Processing...
            </>
          ) : (
            'Ready for New Logic'
          )}
        </Button>
      </CardContent>
    </Card>
  );
}
