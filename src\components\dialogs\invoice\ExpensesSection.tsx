
import React from "react";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Plus, X } from "lucide-react";
import type { InvoiceExpenseItem } from "../../../types";

interface ExpensesSectionProps {
  expenses: InvoiceExpenseItem[];
  newExpense: InvoiceExpenseItem;
  setNewExpense: (expense: InvoiceExpenseItem) => void;
  onAddExpense: () => void;
  onRemoveExpense: (index: number) => void;
  validationErrors?: { [key: string]: string };
}

const expenseCategories = [
  "Office",
  "Marketing", 
  "Equipment",
  "Utilities",
  "Travel",
  "Parts",
  "Labor",
  "Shipping",
  "Other"
];

export function ExpensesSection({
  expenses,
  newExpense,
  setNewExpense,
  onAddExpense,
  onRemoveExpense,
  validationErrors = {}
}: ExpensesSectionProps) {
  return (
    <div className="space-y-3">
      <Label>Additional Expenses (Will be added to Expense Tracking)</Label>
      <div className="grid grid-cols-3 gap-2">
        <Input
          placeholder="Description"
          value={newExpense.description}
          onChange={(e) => setNewExpense({...newExpense, description: e.target.value})}
        />
        <Input
          placeholder="₹0"
          value={newExpense.amount}
          onChange={(e) => setNewExpense({...newExpense, amount: e.target.value})}
        />
        <div className="flex gap-1">
          <Select value={newExpense.category} onValueChange={(value) => setNewExpense({...newExpense, category: value as any})}>
            <SelectTrigger>
              <SelectValue placeholder="Category" />
            </SelectTrigger>
            <SelectContent className="bg-white z-50">
              {expenseCategories.map(category => (
                <SelectItem key={category} value={category}>{category}</SelectItem>
              ))}
            </SelectContent>
          </Select>
          <Button type="button" onClick={onAddExpense} size="sm">
            <Plus className="w-4 h-4" />
          </Button>
        </div>
      </div>
      {expenses && expenses.length > 0 && (
        <div className="space-y-2">
          <p className="text-sm text-orange-600 font-medium">These expenses will be automatically added to your expense tracking system:</p>
          {expenses.map((expense, index) => (
            <div key={index} className="flex items-center justify-between bg-orange-50 p-2 rounded border border-orange-200">
              <span>{expense.description} - {expense.amount} ({expense.category})</span>
              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={() => onRemoveExpense(index)}
              >
                <X className="w-4 h-4" />
              </Button>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
