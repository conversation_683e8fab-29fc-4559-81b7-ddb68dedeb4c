
import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Tit<PERSON> } from "@/components/ui/card";
import type { Service } from "../../types";

interface ServiceReportProps {
  services: Service[];
}

export function ServiceReport({ services }: ServiceReportProps) {
  const completedServices = services.filter(s => s.status === 'Completed').length;
  const pendingServices = services.filter(s => s.status === 'Pending').length;
  const inProgressServices = services.filter(s => s.status === 'In Progress').length;

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Service Summary</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold">{services.length}</div>
              <div className="text-sm text-gray-600">Total Services</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">{completedServices}</div>
              <div className="text-sm text-gray-600">Completed</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">{inProgressServices}</div>
              <div className="text-sm text-gray-600">In Progress</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600">{pendingServices}</div>
              <div className="text-sm text-gray-600">Pending</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
