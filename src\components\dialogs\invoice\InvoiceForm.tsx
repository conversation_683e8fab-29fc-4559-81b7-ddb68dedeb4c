
import React from "react";
import { CustomerInformationForm } from "./forms/CustomerInformationForm";
import { DeviceInformationForm } from "./forms/DeviceInformationForm";
import { BillingInformationForm } from "./forms/BillingInformationForm";
import type { Invoice } from "../../../types";

interface InvoiceFormProps {
  formData: Invoice;
  setFormData: (data: Invoice) => void;
  validationErrors?: { [key: string]: string };
}

const calculateExpectedDelivery = (billDate: string): string => {
  if (!billDate) return "";
  const date = new Date(billDate);
  date.setDate(date.getDate() + 3);
  return date.toISOString().split('T')[0];
};

export function InvoiceForm({ formData, setFormData, validationErrors = {} }: InvoiceFormProps) {
  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold mb-4">Customer Information</h3>
        <CustomerInformationForm 
          formData={formData} 
          setFormData={setFormData}
          validationErrors={validationErrors}
        />
      </div>

      <div>
        <h3 className="text-lg font-semibold mb-4">Device Information</h3>
        <DeviceInformationForm 
          formData={formData} 
          setFormData={setFormData}
          validationErrors={validationErrors}
        />
      </div>

      <div>
        <h3 className="text-lg font-semibold mb-4">Billing Information</h3>
        <BillingInformationForm 
          formData={formData} 
          setFormData={setFormData}
          calculateExpectedDelivery={calculateExpectedDelivery}
          validationErrors={validationErrors}
        />
      </div>
    </div>
  );
}
