
import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ChevronRight, Phone, Mail, Calendar, Package } from 'lucide-react';

interface MobileInvoiceCardProps {
  invoice: any;
  onView: () => void;
  onEdit: () => void;
}

export function MobileInvoiceCard({ invoice, onView, onEdit }: MobileInvoiceCardProps) {
  return (
    <Card className="mb-4 shadow-sm hover:shadow-md transition-shadow">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg font-semibold">#{invoice.id}</CardTitle>
          <Badge variant={
            invoice.status === 'Paid' ? 'default' :
            invoice.status === 'Pending' ? 'secondary' :
            invoice.status === 'Overdue' ? 'destructive' : 'outline'
          }>
            {invoice.status}
          </Badge>
        </div>
        <div className="text-sm text-gray-600">
          <p className="font-medium">{invoice.customer}</p>
          <p>{invoice.date}</p>
        </div>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="space-y-2 mb-4">
          {invoice.device && (
            <div className="flex items-center text-sm">
              <Package className="w-4 h-4 mr-2 text-gray-500" />
              <span>{invoice.device}</span>
            </div>
          )}
          {invoice.phone && (
            <div className="flex items-center text-sm">
              <Phone className="w-4 h-4 mr-2 text-gray-500" />
              <span>{invoice.phone}</span>
            </div>
          )}
        </div>
        
        <div className="flex items-center justify-between">
          <div className="text-xl font-bold text-orange-600">
            {invoice.amount}
          </div>
          <div className="flex gap-2">
            <Button size="sm" variant="outline" onClick={onView}>
              View
            </Button>
            <Button size="sm" onClick={onEdit}>
              Edit
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

interface MobileServiceCardProps {
  service: any;
  onView: () => void;
  onEdit: () => void;
}

export function MobileServiceCard({ service, onView, onEdit }: MobileServiceCardProps) {
  return (
    <Card className="mb-4 shadow-sm hover:shadow-md transition-shadow">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg font-semibold">#{service.id}</CardTitle>
          <Badge variant={
            service.status === 'Completed' ? 'default' :
            service.status === 'In Progress' ? 'secondary' :
            service.status === 'Pending' ? 'outline' : 'destructive'
          }>
            {service.status}
          </Badge>
        </div>
        <div className="text-sm text-gray-600">
          <p className="font-medium">{service.customer}</p>
          <p>{service.device}</p>
        </div>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="space-y-2 mb-4">
          <div className="flex items-center text-sm">
            <Calendar className="w-4 h-4 mr-2 text-gray-500" />
            <span>{service.date}</span>
          </div>
          {service.issue && (
            <p className="text-sm text-gray-600 line-clamp-2">
              {service.issue}
            </p>
          )}
        </div>
        
        <div className="flex items-center justify-between">
          <div className="text-sm">
            <Badge variant="outline" className="text-xs">
              {service.priority} Priority
            </Badge>
          </div>
          <div className="flex gap-2">
            <Button size="sm" variant="outline" onClick={onView}>
              View
            </Button>
            <Button size="sm" onClick={onEdit}>
              Edit
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

interface MobileCustomerCardProps {
  customer: any;
  onView: () => void;
  onEdit: () => void;
}

export function MobileCustomerCard({ customer, onView, onEdit }: MobileCustomerCardProps) {
  return (
    <Card className="mb-4 shadow-sm hover:shadow-md transition-shadow">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg font-semibold">{customer.name}</CardTitle>
          <ChevronRight className="w-5 h-5 text-gray-400" />
        </div>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="space-y-2 mb-4">
          <div className="flex items-center text-sm">
            <Phone className="w-4 h-4 mr-2 text-gray-500" />
            <span>{customer.phone}</span>
          </div>
          {customer.email && (
            <div className="flex items-center text-sm">
              <Mail className="w-4 h-4 mr-2 text-gray-500" />
              <span>{customer.email}</span>
            </div>
          )}
        </div>
        
        <div className="flex items-center justify-between">
          <div className="text-sm text-gray-600">
            <p>Services: {customer.services || 0}</p>
            <p>Total Spent: {customer.totalSpent || '₹0'}</p>
          </div>
          <div className="flex gap-2">
            <Button size="sm" variant="outline" onClick={onView}>
              View
            </Button>
            <Button size="sm" onClick={onEdit}>
              Edit
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

export function MobileFilters({ children }: { children: React.ReactNode }) {
  return (
    <div className="lg:hidden mb-4 p-4 bg-gray-50 rounded-lg">
      <div className="space-y-3">
        {children}
      </div>
    </div>
  );
}

export function MobileStatsGrid({ children }: { children: React.ReactNode }) {
  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-6">
      {children}
    </div>
  );
}
