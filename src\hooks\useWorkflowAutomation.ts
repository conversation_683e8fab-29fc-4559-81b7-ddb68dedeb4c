
import { useCallback } from 'react';
import { toast } from '@/hooks/use-toast';
import type { Invoice, Service, Jobsheet } from '@/types';

export function useWorkflowAutomation() {
  const sendStatusNotification = useCallback((type: 'invoice' | 'service' | 'jobsheet', item: any, oldStatus: string, newStatus: string) => {
    // Simulate notification sending
    console.log(`Status change notification: ${type} ${item.id} changed from ${oldStatus} to ${newStatus}`);
    
    toast({
      title: `${type.charAt(0).toUpperCase() + type.slice(1)} Status Updated`,
      description: `${type.charAt(0).toUpperCase() + type.slice(1)} #${item.id} status changed from ${oldStatus} to ${newStatus}`,
    });

    // Here you would typically send actual notifications via email, SMS, or push notifications
    // For now, we'll just show a toast and log to console
  }, []);

  const autoAssignTechnician = useCallback((jobsheet: Jobsheet) => {
    // Simple auto-assignment logic based on device type and priority
    const technicians = {
      'Laptop': '<PERSON>',
      'Desktop': '<PERSON>',
      'Mobile': '<PERSON>',
      'Tablet': '<PERSON>',
      'Other': 'Tech Support'
    };

    const assignedTechnician = technicians[jobsheet.device_type] || technicians['Other'];
    
    if (jobsheet.priority === 'Urgent') {
      toast({
        title: 'Urgent Job Alert',
        description: `Urgent priority job #${jobsheet.id} has been auto-assigned to ${assignedTechnician}`,
        variant: 'destructive'
      });
    }

    return assignedTechnician;
  }, []);

  const checkDeliveryReminders = useCallback((invoices: Invoice[]) => {
    const today = new Date();
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    const dueTomorrow = invoices.filter(invoice => {
      if (!invoice.expectedDelivery) return false;
      const deliveryDate = new Date(invoice.expectedDelivery);
      return deliveryDate.toDateString() === tomorrow.toDateString();
    });

    if (dueTomorrow.length > 0) {
      toast({
        title: 'Delivery Reminders',
        description: `${dueTomorrow.length} device(s) are scheduled for delivery tomorrow`,
      });
    }

    return dueTomorrow;
  }, []);

  const generateWorkflowSuggestions = useCallback((services: Service[], invoices: Invoice[]) => {
    const suggestions = [];

    // Check for services that need invoicing
    const completedServices = services.filter(s => s.status === 'Completed');
    const invoicedServiceIds = invoices.map(i => i.service_id).filter(Boolean);
    const needInvoicing = completedServices.filter(s => !invoicedServiceIds.includes(s.id));

    if (needInvoicing.length > 0) {
      suggestions.push({
        type: 'create_invoice',
        message: `${needInvoicing.length} completed service(s) need invoicing`,
        action: 'Create Invoices',
        items: needInvoicing
      });
    }

    // Check for overdue invoices
    const overdueInvoices = invoices.filter(i => {
      if (i.status === 'Paid') return false;
      if (!i.expectedDelivery) return false;
      return new Date(i.expectedDelivery) < new Date();
    });

    if (overdueInvoices.length > 0) {
      suggestions.push({
        type: 'follow_up',
        message: `${overdueInvoices.length} invoice(s) are overdue`,
        action: 'Send Reminders',
        items: overdueInvoices
      });
    }

    return suggestions;
  }, []);

  const autoUpdateInventory = useCallback((usedParts: any[]) => {
    // This would typically update the inventory in the database
    console.log('Auto-updating inventory for used parts:', usedParts);
    
    const lowStockWarnings = usedParts.filter(part => {
      // Simulate checking if parts are running low
      return Math.random() > 0.7; // Random simulation
    });

    if (lowStockWarnings.length > 0) {
      toast({
        title: 'Low Stock Alert',
        description: `${lowStockWarnings.length} part(s) are running low on inventory`,
        variant: 'destructive'
      });
    }
  }, []);

  return {
    sendStatusNotification,
    autoAssignTechnician,
    checkDeliveryReminders,
    generateWorkflowSuggestions,
    autoUpdateInventory
  };
}
