
import { useState, useCallback } from 'react';
import { useAppData } from '@/contexts/AppDataContext';
import { useToast } from '@/hooks/use-toast';
import type { Service, Jobsheet, Invoice } from '@/types';

export function useBulkOperations() {
  const [selectedItems, setSelectedItems] = useState<string[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const { updateService, updateJobsheet, updateInvoice, deleteService, deleteJobsheet, deleteInvoice } = useAppData();
  const { toast } = useToast();

  const toggleItemSelection = useCallback((id: string) => {
    setSelectedItems(prev => 
      prev.includes(id) 
        ? prev.filter(item => item !== id)
        : [...prev, id]
    );
  }, []);

  const selectAllItems = useCallback((ids: string[]) => {
    setSelectedItems(ids);
  }, []);

  const clearSelection = useCallback(() => {
    setSelectedItems([]);
  }, []);

  const bulkUpdateServices = useCallback(async (updates: Partial<Service>) => {
    if (selectedItems.length === 0) {
      toast({
        title: "No items selected",
        description: "Please select items to update",
        variant: "destructive"
      });
      return;
    }

    setIsProcessing(true);
    try {
      await Promise.all(
        selectedItems.map(id => updateService(id, updates))
      );
      
      toast({
        title: "Bulk Update Successful",
        description: `Updated ${selectedItems.length} services`
      });
      
      clearSelection();
    } catch (error) {
      toast({
        title: "Bulk Update Failed",
        description: "Some updates may have failed",
        variant: "destructive"
      });
    } finally {
      setIsProcessing(false);
    }
  }, [selectedItems, updateService, toast, clearSelection]);

  const bulkUpdateJobsheets = useCallback(async (updates: Partial<Jobsheet>) => {
    if (selectedItems.length === 0) {
      toast({
        title: "No items selected",
        description: "Please select items to update",
        variant: "destructive"
      });
      return;
    }

    setIsProcessing(true);
    try {
      await Promise.all(
        selectedItems.map(id => updateJobsheet(id, updates))
      );
      
      toast({
        title: "Bulk Update Successful",
        description: `Updated ${selectedItems.length} jobsheets`
      });
      
      clearSelection();
    } catch (error) {
      toast({
        title: "Bulk Update Failed",
        description: "Some updates may have failed",
        variant: "destructive"
      });
    } finally {
      setIsProcessing(false);
    }
  }, [selectedItems, updateJobsheet, toast, clearSelection]);

  const bulkUpdateInvoices = useCallback(async (updates: Partial<Invoice>) => {
    if (selectedItems.length === 0) {
      toast({
        title: "No items selected",
        description: "Please select items to update",
        variant: "destructive"
      });
      return;
    }

    setIsProcessing(true);
    try {
      await Promise.all(
        selectedItems.map(id => updateInvoice(id, updates))
      );
      
      toast({
        title: "Bulk Update Successful",
        description: `Updated ${selectedItems.length} invoices`
      });
      
      clearSelection();
    } catch (error) {
      toast({
        title: "Bulk Update Failed",
        description: "Some updates may have failed",
        variant: "destructive"
      });
    } finally {
      setIsProcessing(false);
    }
  }, [selectedItems, updateInvoice, toast, clearSelection]);

  const bulkDeleteServices = useCallback(async () => {
    if (selectedItems.length === 0) {
      toast({
        title: "No items selected",
        description: "Please select items to delete",
        variant: "destructive"
      });
      return;
    }

    setIsProcessing(true);
    try {
      await Promise.all(
        selectedItems.map(id => deleteService(id))
      );
      
      toast({
        title: "Bulk Delete Successful",
        description: `Deleted ${selectedItems.length} services`
      });
      
      clearSelection();
    } catch (error) {
      toast({
        title: "Bulk Delete Failed",
        description: "Some deletions may have failed",
        variant: "destructive"
      });
    } finally {
      setIsProcessing(false);
    }
  }, [selectedItems, deleteService, toast, clearSelection]);

  const bulkDeleteJobsheets = useCallback(async () => {
    if (selectedItems.length === 0) {
      toast({
        title: "No items selected",
        description: "Please select items to delete",
        variant: "destructive"
      });
      return;
    }

    setIsProcessing(true);
    try {
      await Promise.all(
        selectedItems.map(id => deleteJobsheet(id))
      );
      
      toast({
        title: "Bulk Delete Successful",
        description: `Deleted ${selectedItems.length} jobsheets`
      });
      
      clearSelection();
    } catch (error) {
      toast({
        title: "Bulk Delete Failed",
        description: "Some deletions may have failed",
        variant: "destructive"
      });
    } finally {
      setIsProcessing(false);
    }
  }, [selectedItems, deleteJobsheet, toast, clearSelection]);

  const bulkDeleteInvoices = useCallback(async () => {
    if (selectedItems.length === 0) {
      toast({
        title: "No items selected",
        description: "Please select items to delete",
        variant: "destructive"
      });
      return;
    }

    setIsProcessing(true);
    try {
      await Promise.all(
        selectedItems.map(id => deleteInvoice(id))
      );
      
      toast({
        title: "Bulk Delete Successful",
        description: `Deleted ${selectedItems.length} invoices`
      });
      
      clearSelection();
    } catch (error) {
      toast({
        title: "Bulk Delete Failed",
        description: "Some deletions may have failed",
        variant: "destructive"
      });
    } finally {
      setIsProcessing(false);
    }
  }, [selectedItems, deleteInvoice, toast, clearSelection]);

  return {
    selectedItems,
    isProcessing,
    toggleItemSelection,
    selectAllItems,
    clearSelection,
    bulkUpdateServices,
    bulkUpdateJobsheets,
    bulkUpdateInvoices,
    bulkDeleteServices,
    bulkDeleteJobsheets,
    bulkDeleteInvoices
  };
}
