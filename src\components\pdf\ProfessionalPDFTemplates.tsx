
import { useCallback } from 'react';
import type { Jobsheet, Invoice } from '@/types';

export function useProfessionalPDFTemplates() {
  const generateEnhancedJobsheetPDF = useCallback(async (jobsheet: Jobsheet) => {
    const printWindow = window.open('', '_blank');
    if (!printWindow) return;

    const htmlContent = `
      <!DOCTYPE html>
      <html>
        <head>
          <title>Jobsheet - ${jobsheet.id}</title>
          <style>
            * { margin: 0; padding: 0; box-sizing: border-box; }
            body { 
              font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
              line-height: 1.4;
              color: #2c3e50;
              background: #fff;
            }
            .container { max-width: 800px; margin: 0 auto; padding: 30px; }
            .header { 
              background: linear-gradient(135deg, #f97316 0%, #ea580c 100%);
              color: white;
              padding: 25px;
              border-radius: 10px;
              margin-bottom: 30px;
              position: relative;
              overflow: hidden;
            }
            .header::before {
              content: '';
              position: absolute;
              top: -50%;
              right: -50%;
              width: 200%;
              height: 200%;
              background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            }
            .header-content { position: relative; z-index: 1; }
            .company-name { font-size: 28px; font-weight: 700; margin-bottom: 5px; }
            .tagline { font-size: 14px; opacity: 0.9; }
            .job-info { 
              display: grid; 
              grid-template-columns: 1fr 1fr; 
              gap: 20px; 
              margin: 20px 0; 
            }
            .job-number { 
              font-size: 24px; 
              font-weight: 600; 
              color: #f97316; 
            }
            .section { 
              background: #f8fafc;
              border: 1px solid #e2e8f0;
              border-radius: 8px;
              padding: 20px;
              margin-bottom: 20px;
            }
            .section-title { 
              color: #1e293b;
              font-size: 18px;
              font-weight: 600;
              margin-bottom: 15px;
              padding-bottom: 8px;
              border-bottom: 2px solid #f97316;
              display: flex;
              align-items: center;
            }
            .section-title::before {
              content: '';
              width: 4px;
              height: 18px;
              background: #f97316;
              margin-right: 10px;
              border-radius: 2px;
            }
            .info-grid { 
              display: grid; 
              grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
              gap: 15px;
            }
            .info-item { 
              background: white;
              padding: 12px;
              border-radius: 6px;
              border-left: 3px solid #f97316;
            }
            .info-label { 
              font-weight: 600; 
              color: #64748b;
              font-size: 12px;
              text-transform: uppercase;
              letter-spacing: 0.5px;
              margin-bottom: 4px;
            }
            .info-value { 
              color: #1e293b;
              font-size: 14px;
              font-weight: 500;
            }
            .status-badge {
              display: inline-block;
              padding: 6px 12px;
              border-radius: 20px;
              font-size: 12px;
              font-weight: 600;
              text-transform: uppercase;
              letter-spacing: 0.5px;
            }
            .status-received { background: #dbeafe; color: #1d4ed8; }
            .status-progress { background: #fef3c7; color: #d97706; }
            .status-completed { background: #d1fae5; color: #059669; }
            .status-delivered { background: #e0e7ff; color: #5b21b6; }
            .priority-high { background: #fecaca; color: #dc2626; }
            .priority-medium { background: #fed7aa; color: #ea580c; }
            .priority-low { background: #dcfce7; color: #16a34a; }
            .signature-section {
              display: grid;
              grid-template-columns: 1fr 1fr;
              gap: 30px;
              margin-top: 40px;
              padding-top: 30px;
              border-top: 2px solid #e2e8f0;
            }
            .signature-box {
              text-align: center;
              padding: 20px;
              border: 2px dashed #cbd5e1;
              border-radius: 8px;
              background: #f8fafc;
            }
            .signature-line {
              width: 100%;
              height: 60px;
              border-bottom: 2px solid #94a3b8;
              margin-bottom: 10px;
            }
            .signature-label {
              font-weight: 600;
              color: #475569;
              margin-bottom: 5px;
            }
            .footer {
              text-align: center;
              margin-top: 40px;
              padding-top: 20px;
              border-top: 1px solid #e2e8f0;
              color: #64748b;
              font-size: 12px;
            }
            @media print {
              body { margin: 0; }
              .no-print { display: none !important; }
              .container { padding: 0; max-width: none; }
            }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <div class="header-content">
                <div class="company-name">Binary Solutions</div>
                <div class="tagline">Professional Device Repair Services</div>
                <div class="job-info">
                  <div>
                    <div class="job-number">Job #${jobsheet.id}</div>
                    <div>Date: ${new Date(jobsheet.date).toLocaleDateString('en-US', { 
                      weekday: 'long', 
                      year: 'numeric', 
                      month: 'long', 
                      day: 'numeric' 
                    })}</div>
                  </div>
                  <div style="text-align: right;">
                    <div class="status-badge status-${jobsheet.status.toLowerCase().replace(' ', '')}">${jobsheet.status}</div>
                    <div style="margin-top: 8px;">
                      <span class="status-badge priority-${jobsheet.priority.toLowerCase()}">${jobsheet.priority} Priority</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <div class="section">
              <div class="section-title">Customer Information</div>
              <div class="info-grid">
                <div class="info-item">
                  <div class="info-label">Customer Name</div>
                  <div class="info-value">${jobsheet.customer_name}</div>
                </div>
                <div class="info-item">
                  <div class="info-label">Phone Number</div>
                  <div class="info-value">${jobsheet.phone}</div>
                </div>
                ${jobsheet.alternate_phone ? `
                <div class="info-item">
                  <div class="info-label">Alternate Phone</div>
                  <div class="info-value">${jobsheet.alternate_phone}</div>
                </div>
                ` : ''}
                ${jobsheet.email ? `
                <div class="info-item">
                  <div class="info-label">Email Address</div>
                  <div class="info-value">${jobsheet.email}</div>
                </div>
                ` : ''}
                ${jobsheet.address ? `
                <div class="info-item" style="grid-column: 1 / -1;">
                  <div class="info-label">Address</div>
                  <div class="info-value">${jobsheet.address}</div>
                </div>
                ` : ''}
              </div>
            </div>

            <div class="section">
              <div class="section-title">Device Information</div>
              <div class="info-grid">
                <div class="info-item">
                  <div class="info-label">Device Type</div>
                  <div class="info-value">${jobsheet.device_type}</div>
                </div>
                <div class="info-item">
                  <div class="info-label">Brand</div>
                  <div class="info-value">${jobsheet.device_brand}</div>
                </div>
                <div class="info-item">
                  <div class="info-label">Model</div>
                  <div class="info-value">${jobsheet.device_model}</div>
                </div>
                ${jobsheet.serial_number ? `
                <div class="info-item">
                  <div class="info-label">Serial Number</div>
                  <div class="info-value">${jobsheet.serial_number}</div>
                </div>
                ` : ''}
              </div>
            </div>

            <div class="section">
              <div class="section-title">Service Details</div>
              <div class="info-grid">
                <div class="info-item" style="grid-column: 1 / -1;">
                  <div class="info-label">Reported Issues</div>
                  <div class="info-value">${jobsheet.reported_issues}</div>
                </div>
                <div class="info-item" style="grid-column: 1 / -1;">
                  <div class="info-label">Physical Condition</div>
                  <div class="info-value">${jobsheet.physical_condition}</div>
                </div>
                ${jobsheet.technician_assigned ? `
                <div class="info-item">
                  <div class="info-label">Assigned Technician</div>
                  <div class="info-value">${jobsheet.technician_assigned}</div>
                </div>
                ` : ''}
                ${jobsheet.estimated_cost ? `
                <div class="info-item">
                  <div class="info-label">Estimated Cost</div>
                  <div class="info-value">${jobsheet.estimated_cost}</div>
                </div>
                ` : ''}
                ${jobsheet.expected_delivery ? `
                <div class="info-item">
                  <div class="info-label">Expected Delivery</div>
                  <div class="info-value">${new Date(jobsheet.expected_delivery).toLocaleDateString()}</div>
                </div>
                ` : ''}
              </div>
            </div>

            ${jobsheet.accessories_received ? `
            <div class="section">
              <div class="section-title">Accessories Received</div>
              <div class="info-value" style="padding: 10px; background: white; border-radius: 6px;">
                ${jobsheet.accessories_received}
              </div>
            </div>
            ` : ''}

            ${jobsheet.warranty_terms ? `
            <div class="section">
              <div class="section-title">Warranty Terms</div>
              <div class="info-value" style="padding: 10px; background: white; border-radius: 6px;">
                ${jobsheet.warranty_terms}
              </div>
            </div>
            ` : ''}

            <div class="signature-section">
              <div class="signature-box">
                <div class="signature-label">Customer Signature</div>
                <div class="signature-line"></div>
                <div>${jobsheet.customer_signature ? '✓ Signed' : 'Pending Signature'}</div>
              </div>
              <div class="signature-box">
                <div class="signature-label">Received By</div>
                <div class="signature-line"></div>
                <div>${jobsheet.received_by}</div>
              </div>
            </div>

            <div class="footer">
              <p>Thank you for choosing Binary Solutions for your device repair needs.</p>
              <p>For support or inquiries, please contact <NAME_EMAIL></p>
            </div>

            <div class="no-print" style="position: fixed; bottom: 20px; right: 20px; z-index: 1000;">
              <button onclick="window.print()" style="background: #f97316; color: white; border: none; padding: 12px 24px; border-radius: 8px; cursor: pointer; font-weight: 600; margin-right: 10px; box-shadow: 0 4px 12px rgba(249, 115, 22, 0.3);">Print Jobsheet</button>
              <button onclick="window.close()" style="background: #6b7280; color: white; border: none; padding: 12px 24px; border-radius: 8px; cursor: pointer; font-weight: 600; box-shadow: 0 4px 12px rgba(107, 114, 128, 0.3);">Close</button>
            </div>
          </div>
        </body>
      </html>
    `;

    printWindow.document.write(htmlContent);
    printWindow.document.close();
  }, []);

  const generateEnhancedInvoicePDF = useCallback(async (invoice: Invoice) => {
    const printWindow = window.open('', '_blank');
    if (!printWindow) return;

    const htmlContent = `
      <!DOCTYPE html>
      <html>
        <head>
          <title>Invoice - ${invoice.id}</title>
          <style>
            * { margin: 0; padding: 0; box-sizing: border-box; }
            body { 
              font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
              line-height: 1.4;
              color: #2c3e50;
              background: #fff;
            }
            .container { max-width: 800px; margin: 0 auto; padding: 30px; }
            .header { 
              display: flex;
              justify-content: space-between;
              align-items: flex-start;
              margin-bottom: 40px;
              padding-bottom: 20px;
              border-bottom: 3px solid #f97316;
            }
            .company-info h1 { 
              font-size: 32px; 
              color: #f97316; 
              margin-bottom: 5px; 
            }
            .company-info p { 
              color: #64748b; 
              font-size: 14px; 
            }
            .invoice-info { 
              text-align: right; 
            }
            .invoice-number { 
              font-size: 24px; 
              font-weight: 700; 
              color: #1e293b; 
              margin-bottom: 5px; 
            }
            .invoice-date { 
              color: #64748b; 
              font-size: 14px; 
            }
            .status-badge {
              display: inline-block;
              padding: 6px 16px;
              border-radius: 20px;
              font-size: 12px;
              font-weight: 600;
              text-transform: uppercase;
              letter-spacing: 0.5px;
              margin-top: 10px;
            }
            .status-paid { background: #dcfce7; color: #16a34a; }
            .status-pending { background: #fef3c7; color: #d97706; }
            .status-overdue { background: #fecaca; color: #dc2626; }
            .billing-section {
              display: grid;
              grid-template-columns: 1fr 1fr;
              gap: 40px;
              margin: 40px 0;
            }
            .billing-box {
              padding: 25px;
              background: #f8fafc;
              border-radius: 12px;
              border: 1px solid #e2e8f0;
            }
            .billing-title {
              font-size: 18px;
              font-weight: 600;
              color: #1e293b;
              margin-bottom: 15px;
              padding-bottom: 8px;
              border-bottom: 2px solid #f97316;
            }
            .billing-details p {
              margin-bottom: 8px;
            }
            .items-section {
              margin: 40px 0;
            }
            .section-title {
              font-size: 20px;
              font-weight: 600;
              color: #1e293b;
              margin-bottom: 20px;
            }
            .items-table {
              width: 100%;
              border-collapse: collapse;
              background: white;
              border-radius: 12px;
              overflow: hidden;
              box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            }
            .items-table th {
              background: #f97316;
              color: white;
              padding: 15px;
              text-align: left;
              font-weight: 600;
              font-size: 14px;
              text-transform: uppercase;
              letter-spacing: 0.5px;
            }
            .items-table td {
              padding: 15px;
              border-bottom: 1px solid #e2e8f0;
            }
            .items-table tbody tr:hover {
              background: #f8fafc;
            }
            .total-section {
              margin-top: 40px;
              background: #f8fafc;
              padding: 30px;
              border-radius: 12px;
              border: 1px solid #e2e8f0;
            }
            .total-row {
              display: flex;
              justify-content: space-between;
              align-items: center;
              padding: 10px 0;
              border-bottom: 1px solid #e2e8f0;
            }
            .total-row:last-child {
              border-bottom: none;
              font-size: 24px;
              font-weight: 700;
              color: #f97316;
              padding-top: 20px;
              border-top: 2px solid #f97316;
            }
            .terms-section {
              margin-top: 40px;
              padding: 25px;
              background: #f1f5f9;
              border-radius: 12px;
              border-left: 4px solid #f97316;
            }
            .terms-title {
              font-size: 16px;
              font-weight: 600;
              color: #1e293b;
              margin-bottom: 15px;
            }
            .terms-list {
              list-style: none;
              padding: 0;
            }
            .terms-list li {
              padding: 5px 0;
              color: #475569;
            }
            .terms-list li::before {
              content: '✓';
              color: #f97316;
              font-weight: bold;
              margin-right: 10px;
            }
            @media print {
              body { margin: 0; }
              .no-print { display: none !important; }
              .container { padding: 0; max-width: none; }
            }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <div class="company-info">
                <h1>Binary Solutions</h1>
                <p>Professional Device Repair Services</p>
                <p>Email: <EMAIL> | Phone: +****************</p>
              </div>
              <div class="invoice-info">
                <div class="invoice-number">Invoice #${invoice.id}</div>
                <div class="invoice-date">${new Date(invoice.date).toLocaleDateString('en-US', { 
                  year: 'numeric', 
                  month: 'long', 
                  day: 'numeric' 
                })}</div>
                <div class="status-badge status-${invoice.status?.toLowerCase()}">${invoice.status}</div>
              </div>
            </div>
            
            <div class="billing-section">
              <div class="billing-box">
                <div class="billing-title">Bill To</div>
                <div class="billing-details">
                  <p><strong>${invoice.customer}</strong></p>
                  <p>${invoice.phone}</p>
                  ${invoice.email ? `<p>${invoice.email}</p>` : ''}
                  ${invoice.address ? `<p>${invoice.address}</p>` : ''}
                  ${(invoice.city || invoice.state) ? `<p>${invoice.city}${invoice.city && invoice.state ? ', ' : ''}${invoice.state} ${invoice.pincode || ''}</p>` : ''}
                </div>
              </div>
              <div class="billing-box">
                <div class="billing-title">Service Details</div>
                <div class="billing-details">
                  <p><strong>Device:</strong> ${invoice.device}</p>
                  <p><strong>Issue:</strong> ${invoice.issue}</p>
                  ${invoice.inspectionFee ? `<p><strong>Inspection Fee:</strong> ₹${invoice.inspectionFee}</p>` : ''}
                  ${invoice.expectedDelivery ? `<p><strong>Expected Delivery:</strong> ${new Date(invoice.expectedDelivery).toLocaleDateString()}</p>` : ''}
                </div>
              </div>
            </div>

            ${invoice.usedParts && invoice.usedParts.length > 0 ? `
            <div class="items-section">
              <div class="section-title">Parts & Services</div>
              <table class="items-table">
                <thead>
                  <tr>
                    <th>Description</th>
                    <th>Quantity</th>
                    <th>Unit Price</th>
                    <th>Total</th>
                  </tr>
                </thead>
                <tbody>
                  ${invoice.usedParts.map(part => `
                    <tr>
                      <td>
                        <strong>${part.item_name || part.name}</strong>
                        ${(part.manufacturer || part.model || part.serialNo) ? `
                          <br><small style="color: #64748b;">
                            ${part.manufacturer ? `${part.manufacturer} ` : ''}
                            ${part.model ? `${part.model} ` : ''}
                            ${part.serialNo ? `(S/N: ${part.serialNo})` : ''}
                          </small>
                        ` : ''}
                      </td>
                      <td>${part.quantity}</td>
                      <td>₹${part.price.toLocaleString()}</td>
                      <td>₹${(part.quantity * part.price).toLocaleString()}</td>
                    </tr>
                  `).join('')}
                </tbody>
              </table>
            </div>
            ` : ''}

            ${invoice.expenses && invoice.expenses.length > 0 ? `
            <div class="items-section">
              <div class="section-title">Additional Charges</div>
              <table class="items-table">
                <thead>
                  <tr>
                    <th>Description</th>
                    <th>Category</th>
                    <th>Amount</th>
                  </tr>
                </thead>
                <tbody>
                  ${invoice.expenses.map(expense => `
                    <tr>
                      <td><strong>${expense.description}</strong></td>
                      <td><span style="background: #e2e8f0; padding: 4px 8px; border-radius: 4px; font-size: 12px;">${expense.category}</span></td>
                      <td>₹${parseFloat(expense.amount.replace(/[₹,]/g, '')).toLocaleString()}</td>
                    </tr>
                  `).join('')}
                </tbody>
              </table>
            </div>
            ` : ''}

            <div class="total-section">
              ${invoice.inspectionFee ? `
              <div class="total-row">
                <span>Inspection Fee:</span>
                <span>₹${parseFloat(invoice.inspectionFee).toLocaleString()}</span>
              </div>
              ` : ''}
              <div class="total-row">
                <span>Total Amount:</span>
                <span>${invoice.amount}</span>
              </div>
            </div>

            <div class="terms-section">
              <div class="terms-title">Terms & Conditions</div>
              <ul class="terms-list">
                <li>Payment is due within 30 days of invoice date</li>
                <li>Warranty terms as discussed apply to parts and labor</li>
                <li>Please retain this invoice for warranty claims</li>
                <li>All repair work is guaranteed for quality and workmanship</li>
              </ul>
            </div>

            <div class="no-print" style="position: fixed; bottom: 20px; right: 20px; z-index: 1000;">
              <button onclick="window.print()" style="background: #f97316; color: white; border: none; padding: 12px 24px; border-radius: 8px; cursor: pointer; font-weight: 600; margin-right: 10px; box-shadow: 0 4px 12px rgba(249, 115, 22, 0.3);">Print Invoice</button>
              <button onclick="window.close()" style="background: #6b7280; color: white; border: none; padding: 12px 24px; border-radius: 8px; cursor: pointer; font-weight: 600; box-shadow: 0 4px 12px rgba(107, 114, 128, 0.3);">Close</button>
            </div>
          </div>
        </body>
      </html>
    `;

    printWindow.document.write(htmlContent);
    printWindow.document.close();
  }, []);

  return {
    generateEnhancedJobsheetPDF,
    generateEnhancedInvoicePDF
  };
}
