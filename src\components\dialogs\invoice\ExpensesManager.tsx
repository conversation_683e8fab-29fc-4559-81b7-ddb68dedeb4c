
import { useState } from "react";
import { ExpensesSection } from "./ExpensesSection";
import type { InvoiceExpenseItem } from "../../../types";

interface ExpensesManagerProps {
  expenses: InvoiceExpenseItem[];
  onUpdateExpenses: (expenses: InvoiceExpenseItem[]) => void;
  validationErrors?: { [key: string]: string };
}

export function ExpensesManager({ expenses, onUpdateExpenses, validationErrors = {} }: ExpensesManagerProps) {
  const [newExpense, setNewExpense] = useState<InvoiceExpenseItem>({ 
    description: "", 
    amount: "", 
    category: "Parts" 
  });

  const addExpenseToInvoice = () => {
    if (newExpense.description && newExpense.amount) {
      onUpdateExpenses([...expenses, { ...newExpense }]);
      setNewExpense({ description: "", amount: "", category: "Parts" });
    }
  };

  const removeExpenseFromInvoice = (index: number) => {
    onUpdateExpenses(expenses.filter((_, i) => i !== index));
  };

  return (
    <ExpensesSection
      expenses={expenses}
      newExpense={newExpense}
      setNewExpense={setNewExpense}
      onAddExpense={addExpenseToInvoice}
      onRemoveExpense={removeExpenseFromInvoice}
      validationErrors={validationErrors}
    />
  );
}
