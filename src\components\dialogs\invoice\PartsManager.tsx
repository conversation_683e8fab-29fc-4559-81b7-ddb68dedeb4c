
import { useState } from "react";
import { PartsSection } from "./PartsSection";
import type { UsedPart } from "../../../types";

interface PartsManagerProps {
  usedParts: UsedPart[];
  onUpdateParts: (parts: UsedPart[]) => void;
  validationErrors?: { [key: string]: string };
}

export function PartsManager({ usedParts, onUpdateParts, validationErrors = {} }: PartsManagerProps) {
  const [otherPartName, setOtherPartName] = useState("");
  const [manufacturer, setManufacturer] = useState("");
  const [model, setModel] = useState("");
  const [serialNo, setSerialNo] = useState("");
  const [customPartPrice, setCustomPartPrice] = useState<number>(0);
  const [partQuantity, setPartQuantity] = useState(1);

  const addPartToInvoice = () => {
    if (otherPartName.trim()) {
      // Add custom part
      const newPart: UsedPart = { 
        id: "custom-" + Date.now(),
        itemId: "custom-" + Date.now(), 
        item_id: "custom-" + Date.now(),
        quantity: partQuantity, 
        item_name: otherPartName.trim(),
        name: otherPartName.trim(),
        manufacturer: manufacturer.trim() || undefined,
        model: model.trim() || undefined,
        serialNo: serialNo.trim() || undefined,
        serial_no: serialNo.trim() || undefined,
        price: customPartPrice,
        is_custom: true
      };
      onUpdateParts([...usedParts, newPart]);
      setOtherPartName("");
      setManufacturer("");
      setModel("");
      setSerialNo("");
      setCustomPartPrice(0);
      setPartQuantity(1);
    }
  };

  const removePartFromInvoice = (index: number) => {
    onUpdateParts(usedParts.filter((_, i) => i !== index));
  };

  return (
    <PartsSection
      usedParts={usedParts}
      partQuantity={partQuantity}
      setPartQuantity={setPartQuantity}
      onAddPart={addPartToInvoice}
      onRemovePart={removePartFromInvoice}
      otherPartName={otherPartName}
      setOtherPartName={setOtherPartName}
      manufacturer={manufacturer}
      setManufacturer={setManufacturer}
      model={model}
      setModel={setModel}
      serialNo={serialNo}
      setSerialNo={setSerialNo}
      customPartPrice={customPartPrice}
      setCustomPartPrice={setCustomPartPrice}
      validationErrors={validationErrors}
    />
  );
}
