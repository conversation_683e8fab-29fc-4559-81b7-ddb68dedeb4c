
import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Bell, X, CheckCircle, AlertTriangle, Info, Clock } from 'lucide-react';

interface Notification {
  id: string;
  type: 'info' | 'warning' | 'success' | 'error';
  title: string;
  message: string;
  timestamp: Date;
  read: boolean;
  actionable?: boolean;
  action?: () => void;
  actionLabel?: string;
}

interface NotificationCenterProps {
  services: any[];
  invoices: any[];
  inventory: any[];
}

export function NotificationCenter({ services, invoices, inventory }: NotificationCenterProps) {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [showNotifications, setShowNotifications] = useState(false);

  useEffect(() => {
    generateNotifications();
  }, [services, invoices, inventory]);

  const generateNotifications = () => {
    const newNotifications: Notification[] = [];

    // Check for overdue deliveries
    const overdueInvoices = invoices.filter(invoice => {
      if (!invoice.expectedDelivery || invoice.status === 'Paid') return false;
      return new Date(invoice.expectedDelivery) < new Date();
    });

    if (overdueInvoices.length > 0) {
      newNotifications.push({
        id: 'overdue-deliveries',
        type: 'warning',
        title: 'Overdue Deliveries',
        message: `${overdueInvoices.length} device(s) have passed their expected delivery date`,
        timestamp: new Date(),
        read: false,
        actionable: true,
        actionLabel: 'Review',
        action: () => console.log('Review overdue deliveries')
      });
    }

    // Check for low stock items
    const lowStockItems = inventory.filter(item => 
      item.current_stock <= item.minimum_stock
    );

    if (lowStockItems.length > 0) {
      newNotifications.push({
        id: 'low-stock',
        type: 'warning',
        title: 'Low Stock Alert',
        message: `${lowStockItems.length} item(s) are running low on stock`,
        timestamp: new Date(),
        read: false,
        actionable: true,
        actionLabel: 'Reorder',
        action: () => console.log('Reorder items')
      });
    }

    // Check for pending services
    const pendingServices = services.filter(service => service.status === 'Pending');
    if (pendingServices.length > 5) {
      newNotifications.push({
        id: 'pending-services',
        type: 'info',
        title: 'High Pending Services',
        message: `${pendingServices.length} services are pending assignment`,
        timestamp: new Date(),
        read: false,
        actionable: true,
        actionLabel: 'Assign',
        action: () => console.log('Assign services')
      });
    }

    // Check for payment reminders
    const unpaidInvoices = invoices.filter(invoice => 
      invoice.status === 'Pending' && 
      new Date(invoice.date) < new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
    );

    if (unpaidInvoices.length > 0) {
      newNotifications.push({
        id: 'payment-reminders',
        type: 'error',
        title: 'Payment Reminders',
        message: `${unpaidInvoices.length} invoice(s) are unpaid for over a week`,
        timestamp: new Date(),
        read: false,
        actionable: true,
        actionLabel: 'Send Reminder',
        action: () => console.log('Send payment reminders')
      });
    }

    // Success notifications for completed services
    const recentCompletedServices = services.filter(service => {
      if (service.status !== 'Completed') return false;
      const serviceDate = new Date(service.updated_at || service.date);
      const dayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
      return serviceDate > dayAgo;
    });

    if (recentCompletedServices.length > 0) {
      newNotifications.push({
        id: 'completed-services',
        type: 'success',
        title: 'Services Completed',
        message: `${recentCompletedServices.length} service(s) completed in the last 24 hours`,
        timestamp: new Date(),
        read: false
      });
    }

    setNotifications(newNotifications);
  };

  const markAsRead = (id: string) => {
    setNotifications(prev => 
      prev.map(notif => 
        notif.id === id ? { ...notif, read: true } : notif
      )
    );
  };

  const dismissNotification = (id: string) => {
    setNotifications(prev => prev.filter(notif => notif.id !== id));
  };

  const getIcon = (type: string) => {
    switch (type) {
      case 'success': return CheckCircle;
      case 'warning': return AlertTriangle;
      case 'error': return AlertTriangle;
      default: return Info;
    }
  };

  const getIconColor = (type: string) => {
    switch (type) {
      case 'success': return 'text-green-500';
      case 'warning': return 'text-yellow-500';
      case 'error': return 'text-red-500';
      default: return 'text-blue-500';
    }
  };

  const unreadCount = notifications.filter(n => !n.read).length;

  return (
    <div className="relative">
      <Button
        variant="outline"
        size="sm"
        onClick={() => setShowNotifications(!showNotifications)}
        className="relative"
      >
        <Bell className="w-4 h-4" />
        {unreadCount > 0 && (
          <Badge 
            variant="destructive" 
            className="absolute -top-2 -right-2 w-5 h-5 p-0 flex items-center justify-center text-xs"
          >
            {unreadCount}
          </Badge>
        )}
      </Button>

      {showNotifications && (
        <Card className="absolute right-0 top-full mt-2 w-96 max-h-96 overflow-y-auto z-50 shadow-lg">
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <CardTitle className="text-lg">Notifications</CardTitle>
              <Button 
                variant="ghost" 
                size="sm"
                onClick={() => setShowNotifications(false)}
              >
                <X className="w-4 h-4" />
              </Button>
            </div>
          </CardHeader>
          <CardContent className="p-0">
            {notifications.length === 0 ? (
              <div className="p-6 text-center text-gray-500">
                <Bell className="w-8 h-8 mx-auto mb-2 opacity-50" />
                <p>No new notifications</p>
              </div>
            ) : (
              <div className="max-h-80 overflow-y-auto">
                {notifications.map((notification) => {
                  const Icon = getIcon(notification.type);
                  return (
                    <div
                      key={notification.id}
                      className={`p-4 border-b border-gray-100 hover:bg-gray-50 ${
                        !notification.read ? 'bg-blue-50' : ''
                      }`}
                      onClick={() => markAsRead(notification.id)}
                    >
                      <div className="flex items-start space-x-3">
                        <Icon className={`w-5 h-5 mt-0.5 ${getIconColor(notification.type)}`} />
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center justify-between">
                            <h4 className="text-sm font-semibold text-gray-900">
                              {notification.title}
                            </h4>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="p-1 h-auto"
                              onClick={(e) => {
                                e.stopPropagation();
                                dismissNotification(notification.id);
                              }}
                            >
                              <X className="w-3 h-3" />
                            </Button>
                          </div>
                          <p className="text-sm text-gray-600 mt-1">
                            {notification.message}
                          </p>
                          <div className="flex items-center justify-between mt-2">
                            <div className="flex items-center text-xs text-gray-500">
                              <Clock className="w-3 h-3 mr-1" />
                              {notification.timestamp.toLocaleTimeString()}
                            </div>
                            {notification.actionable && (
                              <Button
                                size="sm"
                                variant="outline"
                                className="text-xs h-6"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  notification.action?.();
                                }}
                              >
                                {notification.actionLabel}
                              </Button>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
}
