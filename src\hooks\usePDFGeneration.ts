import { useCallback } from 'react';
import { useProfessionalPDFTemplates } from '@/components/pdf/ProfessionalPDFTemplates';
import type { Jobsheet, Invoice } from '@/types';

export function usePDFGeneration() {
  const { generateEnhancedJobsheetPDF, generateEnhancedInvoicePDF } = useProfessionalPDFTemplates();

  // Keep the original methods for backward compatibility, but use enhanced versions
  const generateJobsheetPDF = useCallback(async (jobsheet: Jobsheet) => {
    return generateEnhancedJobsheetPDF(jobsheet);
  }, [generateEnhancedJobsheetPDF]);

  const generateInvoicePDF = useCallback(async (invoice: Invoice) => {
    return generateEnhancedInvoicePDF(invoice);
  }, [generateEnhancedInvoicePDF]);

  return {
    generateJobsheetPDF,
    generateInvoicePDF,
    generateEnhancedJobsheetPDF,
    generateEnhancedInvoicePDF
  };
}
