
import { Comment } from './common';
import { UsedPart, InvoiceExpenseItem } from './parts';

export interface Invoice {
  id: string;
  customer: string;
  amount: string;
  date: string;
  status?: 'Draft' | 'Pending' | 'Paid' | 'Overdue' | 'Cancelled';
  device?: string;
  deviceType?: string;
  device_type?: string;
  customDeviceName?: string;
  custom_device_name?: string;
  issue?: string;
  phone?: string;
  alternatePhone?: string;
  alternate_phone?: string;
  email?: string;
  address?: string;
  city?: string;
  state?: string;
  pincode?: string;
  expectedDelivery?: string;
  expected_delivery?: string;
  inspectionFee?: string;
  inspection_fee?: string;
  billableWarranty?: string;
  billable_warranty?: string;
  remarks?: string;
  showRemarks?: boolean;
  show_remarks?: boolean;
  gst?: string;
  estimatedAmount?: string;
  estimated_amount?: string;
  tax_amount?: number;
  discount_amount?: number;
  due_date?: string;
  payment_method?: string;
  payment_terms?: string;
  notes?: string;
  customer_id?: string;
  service_id?: string;
  user_id: string;
  usedParts?: UsedPart[];
  expenses?: InvoiceExpenseItem[];
  comments?: Comment[];
}

export interface PaymentRecord {
  id: string;
  invoice_id: string;
  amount: number;
  payment_method: string;
  payment_date: string;
  reference_number?: string;
  notes?: string;
  created_by: string;
}
