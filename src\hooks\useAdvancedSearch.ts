
import { useState, useMemo, useCallback } from 'react';
import type { Service, Jobsheet, Invoice, Customer } from '@/types';

export interface SearchFilters {
  query: string;
  status?: string;
  priority?: string;
  dateFrom?: string;
  dateTo?: string;
  technician?: string;
  deviceType?: string;
  customerName?: string;
  amountMin?: number;
  amountMax?: number;
}

export function useAdvancedSearch<T extends Service | Jobsheet | Invoice | Customer>() {
  const [filters, setFilters] = useState<SearchFilters>({
    query: '',
    status: '',
    priority: '',
    dateFrom: '',
    dateTo: '',
    technician: '',
    deviceType: '',
    customerName: '',
    amountMin: undefined,
    amountMax: undefined
  });

  const [sortBy, setSortBy] = useState<string>('date');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');

  const updateFilter = useCallback((key: keyof SearchFilters, value: any) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  }, []);

  const clearFilters = useCallback(() => {
    setFilters({
      query: '',
      status: '',
      priority: '',
      dateFrom: '',
      dateTo: '',
      technician: '',
      deviceType: '',
      customerName: '',
      amountMin: undefined,
      amountMax: undefined
    });
  }, []);

  const applyFilters = useCallback((items: T[]) => {
    return items.filter((item: any) => {
      // Text search across multiple fields
      if (filters.query) {
        const searchFields = [
          item.customer || item.customer_name || item.name,
          item.device || item.device_brand,
          item.device_model,
          item.issue || item.reported_issues,
          item.phone,
          item.id
        ].filter(Boolean);
        
        const searchText = searchFields.join(' ').toLowerCase();
        if (!searchText.includes(filters.query.toLowerCase())) {
          return false;
        }
      }

      // Status filter
      if (filters.status && item.status !== filters.status) {
        return false;
      }

      // Priority filter
      if (filters.priority && item.priority !== filters.priority) {
        return false;
      }

      // Date range filter
      if (filters.dateFrom || filters.dateTo) {
        const itemDate = new Date(item.date || item.created_at);
        if (filters.dateFrom && itemDate < new Date(filters.dateFrom)) {
          return false;
        }
        if (filters.dateTo && itemDate > new Date(filters.dateTo)) {
          return false;
        }
      }

      // Technician filter
      if (filters.technician && item.technician !== filters.technician && item.technician_assigned !== filters.technician) {
        return false;
      }

      // Device type filter
      if (filters.deviceType && item.device_type !== filters.deviceType) {
        return false;
      }

      // Customer name filter
      if (filters.customerName && 
          !(item.customer || item.customer_name || item.name || '').toLowerCase().includes(filters.customerName.toLowerCase())) {
        return false;
      }

      // Amount range filter
      if (filters.amountMin !== undefined || filters.amountMax !== undefined) {
        let amount = 0;
        if (item.cost) {
          amount = typeof item.cost === 'string' ? parseFloat(item.cost.replace(/[₹,]/g, '')) : item.cost;
        } else if (item.amount) {
          amount = typeof item.amount === 'string' ? parseFloat(item.amount.replace(/[₹,]/g, '')) : item.amount;
        } else if (item.estimated_cost) {
          amount = parseFloat(item.estimated_cost.replace(/[₹,]/g, ''));
        }

        if (filters.amountMin !== undefined && amount < filters.amountMin) {
          return false;
        }
        if (filters.amountMax !== undefined && amount > filters.amountMax) {
          return false;
        }
      }

      return true;
    });
  }, [filters]);

  const applySorting = useCallback((items: T[]) => {
    return [...items].sort((a: any, b: any) => {
      let aValue = a[sortBy];
      let bValue = b[sortBy];

      // Handle different data types
      if (sortBy === 'date' || sortBy === 'created_at') {
        aValue = new Date(aValue || 0).getTime();
        bValue = new Date(bValue || 0).getTime();
      } else if (sortBy === 'cost' || sortBy === 'amount') {
        aValue = typeof aValue === 'string' ? parseFloat(aValue.replace(/[₹,]/g, '')) : (aValue || 0);
        bValue = typeof bValue === 'string' ? parseFloat(bValue.replace(/[₹,]/g, '')) : (bValue || 0);
      } else if (typeof aValue === 'string') {
        aValue = aValue.toLowerCase();
        bValue = (bValue || '').toLowerCase();
      }

      if (aValue < bValue) return sortOrder === 'asc' ? -1 : 1;
      if (aValue > bValue) return sortOrder === 'asc' ? 1 : -1;
      return 0;
    });
  }, [sortBy, sortOrder]);

  const processItems = useCallback((items: T[]) => {
    const filtered = applyFilters(items);
    const sorted = applySorting(filtered);
    return sorted;
  }, [applyFilters, applySorting]);

  return {
    filters,
    sortBy,
    sortOrder,
    updateFilter,
    clearFilters,
    setSortBy,
    setSortOrder,
    processItems,
    hasActiveFilters: Object.values(filters).some(value => value !== '' && value !== undefined)
  };
}
