
import { useState, useEffect } from 'react';
import { Search, Plus } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { useAppData } from '@/contexts/AppDataContext';
import type { Customer } from '@/types';

interface CustomerSearchProps {
  onCustomerSelect: (customer: Customer | null) => void;
  onNewCustomer: () => void;
  selectedCustomer: Customer | null;
}

export function CustomerSearch({ onCustomerSelect, onNewCustomer, selectedCustomer }: CustomerSearchProps) {
  const { customers } = useAppData();
  const [searchTerm, setSearchTerm] = useState('');
  const [isOpen, setIsOpen] = useState(false);
  const [filteredCustomers, setFilteredCustomers] = useState<Customer[]>([]);

  useEffect(() => {
    if (searchTerm.length > 0) {
      const filtered = customers.filter(customer =>
        customer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        customer.phone.includes(searchTerm) ||
        (customer.email && customer.email.toLowerCase().includes(searchTerm.toLowerCase()))
      );
      setFilteredCustomers(filtered);
      setIsOpen(true);
    } else {
      setFilteredCustomers([]);
      setIsOpen(false);
    }
  }, [searchTerm, customers]);

  const handleCustomerSelect = (customer: Customer) => {
    onCustomerSelect(customer);
    setSearchTerm(customer.name);
    setIsOpen(false);
  };

  const handleInputChange = (value: string) => {
    setSearchTerm(value);
    if (!value) {
      onCustomerSelect(null);
    }
  };

  return (
    <div className="relative">
      <div className="flex gap-2">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <Input
            type="text"
            placeholder="Search customer by name, phone, or email..."
            value={selectedCustomer ? selectedCustomer.name : searchTerm}
            onChange={(e) => handleInputChange(e.target.value)}
            className="pl-10"
          />
        </div>
        <Button type="button" variant="outline" onClick={onNewCustomer}>
          <Plus className="h-4 w-4 mr-2" />
          New Customer
        </Button>
      </div>

      {isOpen && filteredCustomers.length > 0 && (
        <div className="absolute top-full left-0 right-0 z-50 bg-white border border-gray-200 rounded-md shadow-lg max-h-60 overflow-y-auto">
          {filteredCustomers.map((customer) => (
            <div
              key={customer.id}
              className="p-3 hover:bg-gray-50 cursor-pointer border-b border-gray-100 last:border-b-0"
              onClick={() => handleCustomerSelect(customer)}
            >
              <div className="font-medium">{customer.name}</div>
              <div className="text-sm text-gray-600">{customer.phone}</div>
              {customer.email && (
                <div className="text-sm text-gray-500">{customer.email}</div>
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
