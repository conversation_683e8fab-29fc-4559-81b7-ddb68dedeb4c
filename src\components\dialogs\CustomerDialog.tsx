
import { useState, useEffect } from "react";
import { Dialog, DialogContent } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { CustomerDialogHeader } from "./customer/CustomerDialogHeader";
import { CustomerPersonalInfo } from "./customer/CustomerPersonalInfo";
import { CustomerAddressInfo } from "./customer/CustomerAddressInfo";
import type { Customer } from "../../types";

interface CustomerDialogProps {
  isOpen: boolean;
  onClose: () => void;
  customer?: Customer;
  onSave: (customer: Customer) => void;
}

export function CustomerDialog({ isOpen, onClose, customer, onSave }: CustomerDialogProps) {
  const [formData, setFormData] = useState<Customer>({
    id: "",
    name: "",
    email: "",
    phone: "+91 ",
    alternatePhone: "",
    address: "",
    state: "",
    city: "",
    pincode: "",
    purchases: 0,
    services: 0,
    totalSpent: "₹0",
    warranty: "N/A",
    user_id: ""
  });

  useEffect(() => {
    if (isOpen) {
      if (customer) {
        setFormData({
          ...customer,
          name: customer.name || "",
          email: customer.email || "",
          phone: customer.phone || "+91 ",
          alternatePhone: customer.alternatePhone || customer.alternate_phone || "",
          address: customer.address || "",
          state: customer.state || "",
          city: customer.city || "",
          pincode: customer.pincode || "",
          purchases: customer.purchases || 0,
          services: customer.services || 0,
          totalSpent: customer.totalSpent || (typeof customer.total_spent === 'number' ? `₹${customer.total_spent}` : "₹0"),
          warranty: customer.warranty || "N/A"
        });
      } else {
        setFormData({
          id: "",
          name: "",
          email: "",
          phone: "+91 ",
          alternatePhone: "",
          address: "",
          state: "",
          city: "",
          pincode: "",
          purchases: 0,
          services: 0,
          totalSpent: "₹0",
          warranty: "N/A",
          user_id: ""
        });
      }
    }
  }, [isOpen, customer]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave(formData);
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[800px] max-h-[90vh] overflow-y-auto">
        <CustomerDialogHeader customer={customer} />
        
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <CustomerPersonalInfo formData={formData} setFormData={setFormData} />
            <CustomerAddressInfo formData={formData} setFormData={setFormData} />
          </div>

          <div className="flex justify-end space-x-2 pt-4 border-t">
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button type="submit" className="bg-orange-500 hover:bg-orange-600">
              {customer ? 'Update' : 'Add'} Customer
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
