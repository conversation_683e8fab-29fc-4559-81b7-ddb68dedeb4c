
export interface UsedPart {
  id: string;
  itemId?: string;
  item_id?: string;
  item_name: string;
  name?: string;
  quantity: number;
  price: number;
  is_custom?: boolean;
  manufacturer?: string;
  model?: string;
  serialNo?: string;
  serial_no?: string;
  invoice_id?: string;
}

export interface InvoiceExpenseItem {
  description: string;
  amount: string;
  category: 'Office' | 'Marketing' | 'Equipment' | 'Utilities' | 'Travel' | 'Parts' | 'Labor' | 'Shipping' | 'Other';
}
