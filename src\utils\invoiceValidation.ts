
export interface ValidationError {
  field: string;
  message: string;
}

export interface InvoiceValidationResult {
  isValid: boolean;
  errors: ValidationError[];
}

export const validateInvoiceData = (formData: any): InvoiceValidationResult => {
  const errors: ValidationError[] = [];

  // Customer validation
  if (!formData.customer?.trim()) {
    errors.push({ field: 'customer', message: 'Customer name is required' });
  }

  // Phone validation
  if (!formData.phone?.trim()) {
    errors.push({ field: 'phone', message: 'Phone number is required' });
  } else if (!/^\d{10,15}$/.test(formData.phone.replace(/\D/g, ''))) {
    errors.push({ field: 'phone', message: 'Please enter a valid phone number (10-15 digits)' });
  }

  // Amount validation
  if (!formData.amount?.trim()) {
    errors.push({ field: 'amount', message: 'Amount is required' });
  } else {
    const numericAmount = parseFloat(formData.amount.replace(/[₹,]/g, ''));
    if (isNaN(numericAmount) || numericAmount <= 0) {
      errors.push({ field: 'amount', message: 'Please enter a valid amount greater than 0' });
    }
  }

  // Date validation
  if (!formData.date) {
    errors.push({ field: 'date', message: 'Service date is required' });
  } else {
    const serviceDate = new Date(formData.date);
    const today = new Date();
    if (serviceDate > today) {
      errors.push({ field: 'date', message: 'Service date cannot be in the future' });
    }
  }

  // Device validation
  if (!formData.device?.trim()) {
    errors.push({ field: 'device', message: 'Device information is required' });
  }

  // Issue validation
  if (!formData.issue?.trim()) {
    errors.push({ field: 'issue', message: 'Issue description is required' });
  }

  // Email validation (if provided)
  if (formData.email?.trim()) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(formData.email)) {
      errors.push({ field: 'email', message: 'Please enter a valid email address' });
    }
  }

  // GST validation (if provided)
  if (formData.gst?.trim()) {
    const gstValue = parseFloat(formData.gst);
    if (isNaN(gstValue) || gstValue < 0 || gstValue > 100) {
      errors.push({ field: 'gst', message: 'GST must be between 0 and 100%' });
    }
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

export const validateExpenseItem = (expense: any): ValidationError[] => {
  const errors: ValidationError[] = [];

  if (!expense.description?.trim()) {
    errors.push({ field: 'description', message: 'Expense description is required' });
  }

  if (!expense.amount?.trim()) {
    errors.push({ field: 'amount', message: 'Expense amount is required' });
  } else {
    const numericAmount = parseFloat(expense.amount.replace(/[₹,]/g, ''));
    if (isNaN(numericAmount) || numericAmount <= 0) {
      errors.push({ field: 'amount', message: 'Please enter a valid amount greater than 0' });
    }
  }

  if (!expense.category?.trim()) {
    errors.push({ field: 'category', message: 'Expense category is required' });
  }

  return errors;
};

export const validateUsedPart = (part: any, inventory: any[]): ValidationError[] => {
  const errors: ValidationError[] = [];

  if (!part.is_custom && part.itemId) {
    const inventoryItem = inventory.find(item => item.id === part.itemId);
    if (!inventoryItem) {
      errors.push({ field: 'part', message: 'Selected part not found in inventory' });
    } else if (inventoryItem.currentStock < part.quantity) {
      errors.push({ 
        field: 'quantity', 
        message: `Insufficient stock. Available: ${inventoryItem.currentStock}, Required: ${part.quantity}` 
      });
    }
  }

  if (part.quantity <= 0) {
    errors.push({ field: 'quantity', message: 'Part quantity must be greater than 0' });
  }

  if (part.is_custom && !part.item_name?.trim()) {
    errors.push({ field: 'item_name', message: 'Custom part name is required' });
  }

  return errors;
};
