import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { supabase } from '@/integrations/supabase/client';
import type { Customer, Service, Invoice, Expense, Jobsheet, Comment } from '@/types';

interface AppDataContextType {
  // Customers
  customers: Customer[];
  addCustomer: (customer: Omit<Customer, 'id'>) => Promise<Customer | null>;
  updateCustomer: (id: string, customer: Partial<Customer>) => Promise<void>;
  deleteCustomer: (id: string) => Promise<void>;
  
  // Services
  services: Service[];
  addService: (service: Omit<Service, 'id'>) => Promise<void>;
  updateService: (id: string, service: Partial<Service>) => Promise<void>;
  deleteService: (id: string) => Promise<void>;
  
  // Invoices
  invoices: Invoice[];
  addInvoice: (invoice: Omit<Invoice, 'id'>) => Promise<void>;
  updateInvoice: (id: string, invoice: Partial<Invoice>) => Promise<void>;
  deleteInvoice: (id: string) => Promise<void>;
  
  // Expenses
  expenses: Expense[];
  addExpense: (expense: Omit<Expense, 'id'>) => Promise<void>;
  updateExpense: (id: string, expense: Partial<Expense>) => Promise<void>;
  deleteExpense: (id: string) => Promise<void>;
  
  // Jobsheets
  jobsheets: Jobsheet[];
  addJobsheet: (jobsheet: Partial<Jobsheet>) => Promise<void>;
  updateJobsheet: (id: string, jobsheet: Partial<Jobsheet>) => Promise<void>;
  deleteJobsheet: (id: string) => Promise<void>;
  
  // Comments
  getCommentsForEntity: (entityId: string, entityType: 'jobsheet' | 'service' | 'invoice' | 'customer') => Promise<Comment[]>;
  addCommentToEntity: (entityId: string, entityType: 'jobsheet' | 'service' | 'invoice' | 'customer', text: string, author?: string, type?: Comment['type']) => Promise<void>;
  
  // Loading state
  isLoading: boolean;
  loading: boolean; // Add this for backward compatibility
}

const AppDataContext = createContext<AppDataContextType | undefined>(undefined);

export function AppDataProvider({ children }: { children: ReactNode }) {
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [services, setServices] = useState<Service[]>([]);
  const [invoices, setInvoices] = useState<Invoice[]>([]);
  const [expenses, setExpenses] = useState<Expense[]>([]);
  const [jobsheets, setJobsheets] = useState<Jobsheet[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Generate a proper UUID for mock user ID
  const mockUserId = 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11';

  // Fetch all data on component mount
  useEffect(() => {
    fetchAllData();
  }, []);

  const fetchAllData = async () => {
    setIsLoading(true);
    try {
      await Promise.all([
        fetchCustomers(),
        fetchServices(),
        fetchInvoices(),
        fetchExpenses(),
        fetchJobsheets()
      ]);
    } catch (error) {
      console.error('Error fetching data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Customer operations
  const fetchCustomers = async () => {
    const { data, error } = await supabase
      .from('customers')
      .select('*')
      .eq('user_id', mockUserId);
    
    if (error) {
      console.error('Error fetching customers:', error);
      return;
    }
    
    const transformedCustomers = data?.map(customer => ({
      id: customer.id,
      name: customer.name,
      phone: customer.phone,
      email: customer.email,
      address: customer.address,
      city: customer.city,
      state: customer.state,
      pincode: customer.pincode,
      alternatePhone: customer.alternate_phone,
      customerType: customer.customer_type,
      creditLimit: customer.credit_limit,
      paymentTerms: customer.payment_terms,
      taxId: customer.tax_id,
      preferredContact: customer.preferred_contact,
      notes: customer.notes,
      lastServiceDate: customer.last_service_date,
      totalSpent: customer.total_spent?.toString() || '0',
      user_id: customer.user_id
    })) || [];
    
    setCustomers(transformedCustomers);
  };

  const addCustomer = async (customerData: Omit<Customer, 'id'>): Promise<Customer | null> => {
    const { data, error } = await supabase
      .from('customers')
      .insert({
        name: customerData.name,
        phone: customerData.phone,
        email: customerData.email,
        address: customerData.address,
        city: customerData.city,
        state: customerData.state,
        pincode: customerData.pincode,
        alternate_phone: customerData.alternatePhone,
        customer_type: customerData.customerType,
        credit_limit: customerData.creditLimit,
        payment_terms: customerData.paymentTerms,
        tax_id: customerData.taxId,
        preferred_contact: customerData.preferredContact,
        notes: customerData.notes,
        last_service_date: customerData.lastServiceDate,
        total_spent: customerData.totalSpent ? parseFloat(customerData.totalSpent) : 0,
        user_id: mockUserId
      })
      .select()
      .single();

    if (error) {
      console.error('Error adding customer:', error);
      return null;
    }

    const newCustomer: Customer = {
      id: data.id,
      name: data.name,
      phone: data.phone,
      email: data.email,
      address: data.address,
      city: data.city,
      state: data.state,
      pincode: data.pincode,
      alternatePhone: data.alternate_phone,
      customerType: data.customer_type,
      creditLimit: data.credit_limit,
      paymentTerms: data.payment_terms,
      taxId: data.tax_id,
      preferredContact: data.preferred_contact,
      notes: data.notes,
      lastServiceDate: data.last_service_date,
      totalSpent: data.total_spent?.toString() || '0',
      user_id: data.user_id
    };

    setCustomers(prev => [...prev, newCustomer]);
    return newCustomer;
  };

  const updateCustomer = async (id: string, customerData: Partial<Customer>) => {
    const { error } = await supabase
      .from('customers')
      .update({
        name: customerData.name,
        phone: customerData.phone,
        email: customerData.email,
        address: customerData.address,
        city: customerData.city,
        state: customerData.state,
        pincode: customerData.pincode,
        alternate_phone: customerData.alternatePhone,
        customer_type: customerData.customerType,
        credit_limit: customerData.creditLimit,
        payment_terms: customerData.paymentTerms,
        tax_id: customerData.taxId,
        preferred_contact: customerData.preferredContact,
        notes: customerData.notes,
        last_service_date: customerData.lastServiceDate,
        total_spent: customerData.totalSpent ? parseFloat(customerData.totalSpent) : undefined
      })
      .eq('id', id);

    if (error) {
      console.error('Error updating customer:', error);
      return;
    }

    setCustomers(prev => prev.map(customer => 
      customer.id === id ? { ...customer, ...customerData } : customer
    ));
  };

  const deleteCustomer = async (id: string) => {
    const { error } = await supabase
      .from('customers')
      .delete()
      .eq('id', id);

    if (error) {
      console.error('Error deleting customer:', error);
      return;
    }

    setCustomers(prev => prev.filter(customer => customer.id !== id));
  };

  // Service operations
  const fetchServices = async () => {
    const { data, error } = await supabase
      .from('services')
      .select('*')
      .eq('user_id', mockUserId);
    
    if (error) {
      console.error('Error fetching services:', error);
      return;
    }
    
    const transformedServices = data?.map(service => ({
      id: service.id,
      customer: service.customer,
      phone: service.phone,
      device: service.device,
      issue: service.issue,
      status: service.status as Service['status'],
      service_type: service.service_type as Service['service_type'],
      priority: service.priority as Service['priority'],
      technician: service.technician,
      estimated_completion: service.estimated_completion,
      actual_completion: service.actual_completion,
      warranty_period: service.warranty_period,
      cost: service.cost,
      parts_cost: service.parts_cost,
      labor_cost: service.labor_cost,
      customer_id: service.customer_id,
      jobsheet_id: service.jobsheet_id,
      user_id: service.user_id,
      comments: [] // Will be fetched separately when needed
    })) || [];
    
    setServices(transformedServices);
  };

  const addService = async (serviceData: Omit<Service, 'id'>) => {
    const { data, error } = await supabase
      .from('services')
      .insert({
        customer: serviceData.customer,
        phone: serviceData.phone,
        device: serviceData.device,
        issue: serviceData.issue,
        status: serviceData.status as string,
        service_type: serviceData.service_type as string,
        priority: serviceData.priority as string,
        technician: serviceData.technician,
        estimated_completion: serviceData.estimated_completion,
        actual_completion: serviceData.actual_completion,
        warranty_period: serviceData.warranty_period,
        cost: serviceData.cost,
        parts_cost: serviceData.parts_cost,
        labor_cost: serviceData.labor_cost,
        customer_id: serviceData.customer_id,
        jobsheet_id: (serviceData as any).jobsheet_id,
        user_id: mockUserId
      })
      .select()
      .single();

    if (error) {
      console.error('Error adding service:', error);
      return;
    }

    const newService: Service = {
      id: data.id,
      customer: data.customer,
      phone: data.phone,
      device: data.device,
      issue: data.issue,
      status: data.status as Service['status'],
      service_type: data.service_type as Service['service_type'],
      priority: data.priority as Service['priority'],
      technician: data.technician,
      estimated_completion: data.estimated_completion,
      actual_completion: data.actual_completion,
      warranty_period: data.warranty_period,
      cost: data.cost,
      parts_cost: data.parts_cost,
      labor_cost: data.labor_cost,
      customer_id: data.customer_id,
      jobsheet_id: data.jobsheet_id,
      user_id: data.user_id,
      comments: []
    };

    setServices(prev => [...prev, newService]);
  };

  const updateService = async (id: string, serviceData: Partial<Service>) => {
    const updateData: any = { ...serviceData };
    
    // Cast enum types to strings for database
    if (updateData.status) updateData.status = updateData.status as string;
    if (updateData.service_type) updateData.service_type = updateData.service_type as string;
    if (updateData.priority) updateData.priority = updateData.priority as string;
    
    const { error } = await supabase
      .from('services')
      .update(updateData)
      .eq('id', id);

    if (error) {
      console.error('Error updating service:', error);
      return;
    }

    setServices(prev => prev.map(service => 
      service.id === id ? { ...service, ...serviceData } : service
    ));
  };

  const deleteService = async (id: string) => {
    const { error } = await supabase
      .from('services')
      .delete()
      .eq('id', id);

    if (error) {
      console.error('Error deleting service:', error);
      return;
    }

    setServices(prev => prev.filter(service => service.id !== id));
  };

  // Invoice operations
  const fetchInvoices = async () => {
    const { data, error } = await supabase
      .from('invoices')
      .select('*')
      .eq('user_id', mockUserId);
    
    if (error) {
      console.error('Error fetching invoices:', error);
      return;
    }
    
    const transformedInvoices = data?.map(invoice => ({
      id: invoice.id,
      customer: invoice.customer,
      phone: invoice.phone,
      device: invoice.device,
      issue: invoice.issue,
      amount: invoice.amount?.toString() || '0',
      date: invoice.date,
      status: (invoice.status as Invoice['status']) || 'Pending',
      user_id: invoice.user_id
    })) || [];
    
    setInvoices(transformedInvoices);
  };

  const addInvoice = async (invoiceData: Omit<Invoice, 'id'>) => {
    const { data, error } = await supabase
      .from('invoices')
      .insert({
        customer: invoiceData.customer,
        phone: invoiceData.phone,
        device: invoiceData.device,
        issue: invoiceData.issue,
        amount: invoiceData.amount ? parseFloat(invoiceData.amount.replace(/[₹,]/g, '')) : 0,
        date: invoiceData.date,
        status: invoiceData.status || 'Pending',
        user_id: mockUserId
      })
      .select()
      .single();

    if (error) {
      console.error('Error adding invoice:', error);
      return;
    }

    const newInvoice: Invoice = {
      id: data.id,
      customer: data.customer,
      phone: data.phone,
      device: data.device,
      issue: data.issue,
      amount: data.amount?.toString() || '0',
      date: data.date,
      status: (data.status as Invoice['status']) || 'Pending',
      user_id: data.user_id
    };

    setInvoices(prev => [...prev, newInvoice]);
  };

  const updateInvoice = async (id: string, invoiceData: Partial<Invoice>) => {
    const { error } = await supabase
      .from('invoices')
      .update({
        customer: invoiceData.customer,
        phone: invoiceData.phone,
        device: invoiceData.device,
        issue: invoiceData.issue,
        amount: invoiceData.amount ? parseFloat(invoiceData.amount.replace(/[₹,]/g, '')) : undefined,
        date: invoiceData.date,
        status: invoiceData.status
      })
      .eq('id', id);

    if (error) {
      console.error('Error updating invoice:', error);
      return;
    }

    setInvoices(prev => prev.map(invoice => 
      invoice.id === id ? { ...invoice, ...invoiceData } : invoice
    ));
  };

  const deleteInvoice = async (id: string) => {
    const { error } = await supabase
      .from('invoices')
      .delete()
      .eq('id', id);

    if (error) {
      console.error('Error deleting invoice:', error);
      return;
    }

    setInvoices(prev => prev.filter(invoice => invoice.id !== id));
  };

  // Expense operations
  const fetchExpenses = async () => {
    const { data, error } = await supabase
      .from('expenses')
      .select('*')
      .eq('user_id', mockUserId);
    
    if (error) {
      console.error('Error fetching expenses:', error);
      return;
    }
    
    const transformedExpenses = data?.map(expense => ({
      id: expense.id,
      date: expense.date,
      description: expense.description,
      category: expense.category as Expense['category'],
      amount: expense.amount?.toString() || '0',
      vendor: expense.vendor,
      receipt: expense.receipt as Expense['receipt'],
      user_id: expense.user_id
    })) || [];
    
    setExpenses(transformedExpenses);
  };

  const addExpense = async (expenseData: Omit<Expense, 'id'>) => {
    const { data, error } = await supabase
      .from('expenses')
      .insert({
        date: expenseData.date,
        description: expenseData.description,
        category: expenseData.category,
        amount: expenseData.amount ? parseFloat(expenseData.amount.replace(/[₹,]/g, '')) : 0,
        vendor: expenseData.vendor,
        receipt: expenseData.receipt || 'Available',
        user_id: mockUserId
      })
      .select()
      .single();

    if (error) {
      console.error('Error adding expense:', error);
      return;
    }

    const newExpense: Expense = {
      id: data.id,
      date: data.date,
      description: data.description,
      category: data.category as Expense['category'],
      amount: data.amount?.toString() || '0',
      vendor: data.vendor,
      receipt: data.receipt as Expense['receipt'],
      user_id: data.user_id
    };

    setExpenses(prev => [...prev, newExpense]);
  };

  const updateExpense = async (id: string, expenseData: Partial<Expense>) => {
    const { error } = await supabase
      .from('expenses')
      .update({
        date: expenseData.date,
        description: expenseData.description,
        category: expenseData.category,
        amount: expenseData.amount ? parseFloat(expenseData.amount.replace(/[₹,]/g, '')) : undefined,
        vendor: expenseData.vendor,
        receipt: expenseData.receipt
      })
      .eq('id', id);

    if (error) {
      console.error('Error updating expense:', error);
      return;
    }

    setExpenses(prev => prev.map(expense => 
      expense.id === id ? { ...expense, ...expenseData } : expense
    ));
  };

  const deleteExpense = async (id: string) => {
    const { error } = await supabase
      .from('expenses')
      .delete()
      .eq('id', id);

    if (error) {
      console.error('Error deleting expense:', error);
      return;
    }

    setExpenses(prev => prev.filter(expense => expense.id !== id));
  };

  // Jobsheet operations
  const fetchJobsheets = async () => {
    const { data, error } = await supabase
      .from('jobsheets')
      .select('*')
      .eq('user_id', mockUserId);
    
    if (error) {
      console.error('Error fetching jobsheets:', error);
      return;
    }
    
    const transformedJobsheets = data?.map(jobsheet => ({
      id: jobsheet.id,
      customer_id: jobsheet.customer_id,
      customer_name: jobsheet.customer_name,
      phone: jobsheet.phone,
      alternate_phone: jobsheet.alternate_phone,
      email: jobsheet.email,
      address: jobsheet.address,
      device_type: jobsheet.device_type as Jobsheet['device_type'],
      device_brand: jobsheet.device_brand,
      device_model: jobsheet.device_model,
      serial_number: jobsheet.serial_number,
      password: jobsheet.password,
      reported_issues: jobsheet.reported_issues,
      physical_condition: jobsheet.physical_condition,
      accessories_received: jobsheet.accessories_received,
      estimated_cost: jobsheet.estimated_cost?.toString(),
      advance_payment: jobsheet.advance_payment?.toString(),
      expected_delivery: jobsheet.expected_delivery,
      technician_assigned: jobsheet.technician_assigned,
      priority: jobsheet.priority as Jobsheet['priority'],
      status: jobsheet.status as Jobsheet['status'],
      warranty_terms: jobsheet.warranty_terms,
      special_instructions: jobsheet.special_instructions,
      received_by: jobsheet.received_by,
      customer_signature: jobsheet.customer_signature,
      terms_accepted: jobsheet.terms_accepted,
      date: jobsheet.date,
      user_id: jobsheet.user_id
    })) || [];
    
    setJobsheets(transformedJobsheets);
  };

  const addJobsheet = async (jobsheetData: Partial<Jobsheet>) => {
    const { data, error } = await supabase
      .from('jobsheets')
      .insert({
        customer_id: (jobsheetData as any).customer_id || null,
        customer_name: jobsheetData.customer_name || '',
        phone: jobsheetData.phone || '',
        alternate_phone: jobsheetData.alternate_phone,
        email: jobsheetData.email,
        address: jobsheetData.address,
        device_type: jobsheetData.device_type || 'Laptop',
        device_brand: jobsheetData.device_brand || '',
        device_model: jobsheetData.device_model || '',
        serial_number: jobsheetData.serial_number,
        password: jobsheetData.password,
        reported_issues: jobsheetData.reported_issues || '',
        physical_condition: jobsheetData.physical_condition || '',
        accessories_received: jobsheetData.accessories_received,
        estimated_cost: jobsheetData.estimated_cost ? parseFloat(jobsheetData.estimated_cost) : null,
        advance_payment: jobsheetData.advance_payment ? parseFloat(jobsheetData.advance_payment) : null,
        expected_delivery: jobsheetData.expected_delivery,
        technician_assigned: jobsheetData.technician_assigned,
        priority: jobsheetData.priority || 'Medium',
        status: jobsheetData.status || 'Received',
        warranty_terms: jobsheetData.warranty_terms,
        special_instructions: jobsheetData.special_instructions,
        received_by: jobsheetData.received_by || 'Front Desk',
        customer_signature: jobsheetData.customer_signature,
        terms_accepted: jobsheetData.terms_accepted,
        date: jobsheetData.date || new Date().toISOString().split('T')[0],
        user_id: mockUserId
      })
      .select()
      .single();

    if (error) {
      console.error('Error adding jobsheet:', error);
      return;
    }

    await fetchJobsheets(); // Refresh the list
  };

  const updateJobsheet = async (id: string, jobsheetData: Partial<Jobsheet>) => {
    const { error } = await supabase
      .from('jobsheets')
      .update({
        customer_id: (jobsheetData as any).customer_id,
        customer_name: jobsheetData.customer_name,
        phone: jobsheetData.phone,
        alternate_phone: jobsheetData.alternate_phone,
        email: jobsheetData.email,
        address: jobsheetData.address,
        device_type: jobsheetData.device_type,
        device_brand: jobsheetData.device_brand,
        device_model: jobsheetData.device_model,
        serial_number: jobsheetData.serial_number,
        password: jobsheetData.password,
        reported_issues: jobsheetData.reported_issues,
        physical_condition: jobsheetData.physical_condition,
        accessories_received: jobsheetData.accessories_received,
        estimated_cost: jobsheetData.estimated_cost ? parseFloat(jobsheetData.estimated_cost) : null,
        advance_payment: jobsheetData.advance_payment ? parseFloat(jobsheetData.advance_payment) : null,
        expected_delivery: jobsheetData.expected_delivery,
        technician_assigned: jobsheetData.technician_assigned,
        priority: jobsheetData.priority,
        status: jobsheetData.status,
        warranty_terms: jobsheetData.warranty_terms,
        special_instructions: jobsheetData.special_instructions,
        received_by: jobsheetData.received_by,
        customer_signature: jobsheetData.customer_signature,
        terms_accepted: jobsheetData.terms_accepted
      })
      .eq('id', id);

    if (error) {
      console.error('Error updating jobsheet:', error);
      return;
    }

    await fetchJobsheets(); // Refresh the list
  };

  const deleteJobsheet = async (id: string) => {
    const { error } = await supabase
      .from('jobsheets')
      .delete()
      .eq('id', id);

    if (error) {
      console.error('Error deleting jobsheet:', error);
      return;
    }

    setJobsheets(prev => prev.filter(jobsheet => jobsheet.id !== id));
  };

  // Comment operations
  const getCommentsForEntity = async (
    entityId: string,
    entityType: 'jobsheet' | 'service' | 'invoice' | 'customer'
  ): Promise<Comment[]> => {
    try {
      const { data, error } = await supabase
        .from('comments')
        .select('*')
        .eq('entity_id', entityId)
        .eq('entity_type', entityType)
        .order('created_at', { ascending: true });

      if (error) {
        console.error('Error fetching comments:', error);
        return [];
      }

      return data?.map(comment => ({
        id: comment.id,
        text: comment.text,
        author: comment.author,
        type: comment.comment_type as Comment['type'],
        created_at: comment.created_at,
        timestamp: comment.timestamp || comment.created_at
      })) || [];
    } catch (error) {
      console.error('Error fetching comments:', error);
      return [];
    }
  };

  const addCommentToEntity = async (
    entityId: string,
    entityType: 'jobsheet' | 'service' | 'invoice' | 'customer',
    text: string,
    author: string = 'Current User',
    type: Comment['type'] = 'internal'
  ) => {
    try {
      const { error } = await supabase
        .from('comments')
        .insert({
          entity_id: entityId,
          entity_type: entityType,
          text,
          author,
          comment_type: type
        });

      if (error) {
        console.error('Error adding comment:', error);
      }
    } catch (error) {
      console.error('Error adding comment:', error);
    }
  };

  const value: AppDataContextType = {
    customers,
    addCustomer,
    updateCustomer,
    deleteCustomer,
    services,
    addService,
    updateService,
    deleteService,
    invoices,
    addInvoice,
    updateInvoice,
    deleteInvoice,
    expenses,
    addExpense,
    updateExpense,
    deleteExpense,
    jobsheets,
    addJobsheet,
    updateJobsheet,
    deleteJobsheet,
    getCommentsForEntity,
    addCommentToEntity,
    isLoading,
    loading: isLoading // Add backward compatibility
  };

  return (
    <AppDataContext.Provider value={value}>
      {children}
    </AppDataContext.Provider>
  );
}

export const useAppData = () => {
  const context = useContext(AppDataContext);
  if (context === undefined) {
    throw new Error('useAppData must be used within an AppDataProvider');
  }
  return context;
};
