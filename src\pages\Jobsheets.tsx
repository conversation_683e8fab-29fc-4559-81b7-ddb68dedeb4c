
import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Plus, Search, Edit, Eye, FileText, Trash2, ExternalLink } from "lucide-react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { JobsheetDialog } from "@/components/dialogs/JobsheetDialog";
import { useToast } from "@/hooks/use-toast";
import { useAppData } from "@/contexts/AppDataContext";
import { supabase } from "@/integrations/supabase/client";

export default function Jobsheets() {
  const { jobsheets, deleteJobsheet } = useAppData();
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("All");
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingJobsheet, setEditingJobsheet] = useState(undefined);
  const [relatedServices, setRelatedServices] = useState<Record<string, any[]>>({});
  const { toast } = useToast();

  // Fetch related services for each jobsheet
  useEffect(() => {
    const fetchRelatedServices = async () => {
      const servicesByJobsheet: Record<string, any[]> = {};
      
      for (const jobsheet of jobsheets) {
        const { data: services } = await supabase
          .from('services')
          .select('*')
          .eq('jobsheet_id', jobsheet.id);
        
        if (services) {
          servicesByJobsheet[jobsheet.id] = services;
        }
      }
      
      setRelatedServices(servicesByJobsheet);
    };

    if (jobsheets.length > 0) {
      fetchRelatedServices();
    }
  }, [jobsheets]);

  const filteredJobsheets = jobsheets.filter(jobsheet => {
    const matchesSearch = jobsheet.customer_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         jobsheet.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         jobsheet.device_brand.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         jobsheet.device_model.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === "All" || jobsheet.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  const handleAddJobsheet = () => {
    setEditingJobsheet(undefined);
    setIsDialogOpen(true);
  };

  const handleEditJobsheet = (jobsheet: any) => {
    setEditingJobsheet(jobsheet);
    setIsDialogOpen(true);
  };

  const handleDeleteJobsheet = (jobsheetId: string) => {
    deleteJobsheet(jobsheetId);
    toast({
      title: "Jobsheet Deleted",
      description: "Jobsheet has been successfully deleted.",
    });
  };

  const handleSaveJobsheet = (jobsheetData: any) => {
    // The form now handles saving directly to the database
    setIsDialogOpen(false);
    toast({
      title: editingJobsheet ? "Jobsheet Updated" : "Jobsheet Created",
      description: `Jobsheet has been successfully ${editingJobsheet ? 'updated' : 'created'}.`,
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Received':
        return 'bg-blue-100 text-blue-800';
      case 'In Progress':
        return 'bg-yellow-100 text-yellow-800';
      case 'Completed':
        return 'bg-green-100 text-green-800';
      case 'Delivered':
        return 'bg-purple-100 text-purple-800';
      case 'Cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'Urgent':
        return 'bg-red-100 text-red-800';
      case 'High':
        return 'bg-orange-100 text-orange-800';
      case 'Medium':
        return 'bg-yellow-100 text-yellow-800';
      case 'Low':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Calculate stats
  const totalJobsheets = jobsheets.length;
  const receivedJobsheets = jobsheets.filter(j => j.status === 'Received').length;
  const inProgressJobsheets = jobsheets.filter(j => j.status === 'In Progress').length;
  const completedJobsheets = jobsheets.filter(j => j.status === 'Completed').length;

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-gray-900">Jobsheet Management</h1>
        <Button onClick={handleAddJobsheet} className="bg-orange-500 hover:bg-orange-600 text-white">
          <Plus className="w-4 h-4 mr-2" />
          New Jobsheet
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Jobsheets</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalJobsheets}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Received</CardTitle>
            <FileText className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{receivedJobsheets}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">In Progress</CardTitle>
            <FileText className="h-4 w-4 text-yellow-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{inProgressJobsheets}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Completed</CardTitle>
            <FileText className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{completedJobsheets}</div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="text-lg font-semibold text-gray-900">Jobsheets</CardTitle>
          <p className="text-sm text-gray-600">Manage device service jobsheets and track progress</p>
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-4 mb-6">
            <div className="relative flex-1 max-w-sm">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="Search jobsheets..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="All">All Status</SelectItem>
                <SelectItem value="Received">Received</SelectItem>
                <SelectItem value="In Progress">In Progress</SelectItem>
                <SelectItem value="Completed">Completed</SelectItem>
                <SelectItem value="Delivered">Delivered</SelectItem>
                <SelectItem value="Cancelled">Cancelled</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>ID</TableHead>
                <TableHead>Customer</TableHead>
                <TableHead>Device</TableHead>
                <TableHead>Issue</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Priority</TableHead>
                <TableHead>Date</TableHead>
                <TableHead>Related Services</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredJobsheets.map((jobsheet) => (
                <TableRow key={jobsheet.id}>
                  <TableCell className="font-medium">{jobsheet.id.slice(0, 8)}</TableCell>
                  <TableCell>
                    <div>
                      <div className="font-medium">{jobsheet.customer_name}</div>
                      <div className="text-sm text-gray-500">{jobsheet.phone}</div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div>
                      <div className="font-medium">{jobsheet.device_brand} {jobsheet.device_model}</div>
                      <div className="text-sm text-gray-500">{jobsheet.device_type}</div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="max-w-xs truncate">{jobsheet.reported_issues}</div>
                  </TableCell>
                  <TableCell>
                    <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(jobsheet.status)}`}>
                      {jobsheet.status}
                    </span>
                  </TableCell>
                  <TableCell>
                    <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${getPriorityColor(jobsheet.priority)}`}>
                      {jobsheet.priority}
                    </span>
                  </TableCell>
                  <TableCell>{jobsheet.date}</TableCell>
                  <TableCell>
                    {relatedServices[jobsheet.id]?.length > 0 ? (
                      <div className="text-sm">
                        <span className="bg-green-100 text-green-800 px-2 py-1 rounded-full">
                          {relatedServices[jobsheet.id].length} service{relatedServices[jobsheet.id].length > 1 ? 's' : ''}
                        </span>
                      </div>
                    ) : (
                      <span className="text-gray-400 text-sm">None</span>
                    )}
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      <Button variant="ghost" size="sm" onClick={() => handleEditJobsheet(jobsheet)}>
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button 
                        variant="ghost" 
                        size="sm" 
                        className="text-red-600 hover:text-red-700"
                        onClick={() => handleDeleteJobsheet(jobsheet.id)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      <JobsheetDialog
        isOpen={isDialogOpen}
        onClose={() => setIsDialogOpen(false)}
        jobsheet={editingJobsheet}
        onSave={handleSaveJobsheet}
      />
    </div>
  );
}
