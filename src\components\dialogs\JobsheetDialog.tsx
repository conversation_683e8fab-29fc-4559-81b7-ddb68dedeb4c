
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog";
import { JobsheetForm } from "@/components/forms/JobsheetForm";
import { WorkflowStatus } from "@/components/workflow/WorkflowStatus";
import { QuickActions } from "@/components/workflow/QuickActions";
import { useAppData } from "@/contexts/AppDataContext";
import type { Jobsheet } from "@/types";

interface JobsheetDialogProps {
  isOpen: boolean;
  onClose: () => void;
  jobsheet?: Jobsheet;
  customerData?: {
    customer_name: string;
    phone: string;
    email?: string;
    address?: string;
    alternate_phone?: string;
  };
  onSave: (data: Partial<Jobsheet>) => void;
}

export function JobsheetDialog({ isOpen, onClose, jobsheet, customerData, onSave }: JobsheetDialogProps) {
  const { services, invoices } = useAppData();

  // Find related service and invoice
  const relatedService = jobsheet ? services.find(s => s.jobsheet_id === jobsheet.id) : undefined;
  const relatedInvoice = relatedService ? invoices.find(i => i.service_id === relatedService.id) : undefined;

  const handleSave = (data: Partial<Jobsheet>) => {
    onSave(data);
    onClose();
  };

  const handleServiceCreated = () => {
    // Refresh data or handle service creation
    console.log('Service created successfully');
  };

  const handleInvoiceCreated = () => {
    // Refresh data or handle invoice creation  
    console.log('Invoice created successfully');
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {jobsheet ? 'Edit Jobsheet' : 'Create New Jobsheet'}
          </DialogTitle>
        </DialogHeader>
        
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Main form takes 3 columns */}
          <div className="lg:col-span-3">
            <JobsheetForm
              jobsheet={jobsheet}
              customerData={customerData}
              onSave={handleSave}
              onCancel={onClose}
            />
          </div>

          {/* Sidebar with workflow info takes 1 column */}
          {jobsheet && (
            <div className="space-y-4">
              <WorkflowStatus
                jobsheet={jobsheet}
                service={relatedService}
                invoice={relatedInvoice}
              />
              
              <QuickActions
                jobsheet={jobsheet}
                service={relatedService}
                onServiceCreated={handleServiceCreated}
                onInvoiceCreated={handleInvoiceCreated}
              />
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}
