
import React from "react";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Plus, X } from "lucide-react";
import type { UsedPart } from "../../../types";

interface PartsSectionProps {
  usedParts: UsedPart[];
  partQuantity: number;
  setPartQuantity: (quantity: number) => void;
  onAddPart: () => void;
  onRemovePart: (index: number) => void;
  otherPartName: string;
  setOtherPartName: (name: string) => void;
  manufacturer: string;
  setManufacturer: (manufacturer: string) => void;
  model: string;
  setModel: (model: string) => void;
  serialNo: string;
  setSerialNo: (serialNo: string) => void;
  customPartPrice: number;
  setCustomPartPrice: (price: number) => void;
  validationErrors?: { [key: string]: string };
}

export function PartsSection({
  usedParts,
  partQuantity,
  setPartQuantity,
  onAddPart,
  onRemovePart,
  otherPartName,
  setOtherPartName,
  manufacturer,
  setManufacturer,
  model,
  setModel,
  serialNo,
  setSerialNo,
  customPartPrice,
  setCustomPartPrice,
  validationErrors = {}
}: PartsSectionProps) {

  const handleAddPart = () => {
    if (otherPartName.trim()) {
      onAddPart();
    }
  };

  return (
    <div className="space-y-3">
      <Label>Parts Used</Label>
      
      <div className="space-y-3 p-3 border rounded-lg bg-gray-50">
        <div>
          <Label htmlFor="otherPart">Part Name</Label>
          <Input
            id="otherPart"
            value={otherPartName}
            onChange={(e) => setOtherPartName(e.target.value)}
            placeholder="Enter part name"
            className="mt-1"
          />
        </div>
        <div className="grid grid-cols-2 gap-2">
          <div>
            <Label htmlFor="manufacturer">Manufacturer</Label>
            <Input
              id="manufacturer"
              value={manufacturer}
              onChange={(e) => setManufacturer(e.target.value)}
              placeholder="Manufacturer"
            />
          </div>
          <div>
            <Label htmlFor="model">Model</Label>
            <Input
              id="model"
              value={model}
              onChange={(e) => setModel(e.target.value)}
              placeholder="Model"
            />
          </div>
        </div>
        <div className="grid grid-cols-3 gap-2">
          <div>
            <Label htmlFor="serialNo">Serial No</Label>
            <Input
              id="serialNo"
              value={serialNo}
              onChange={(e) => setSerialNo(e.target.value)}
              placeholder="Serial No"
            />
          </div>
          <div>
            <Label htmlFor="customPartPrice">Price (₹)</Label>
            <Input
              id="customPartPrice"
              type="number"
              min="0"
              step="0.01"
              value={customPartPrice}
              onChange={(e) => setCustomPartPrice(parseFloat(e.target.value) || 0)}
              placeholder="0.00"
            />
          </div>
          <div>
            <Label htmlFor="quantity">Quantity</Label>
            <Input
              id="quantity"
              type="number"
              min="1"
              value={partQuantity}
              onChange={(e) => setPartQuantity(parseInt(e.target.value) || 1)}
              placeholder="Qty"
            />
          </div>
        </div>
        <Button type="button" onClick={handleAddPart} size="sm" className="w-full">
          <Plus className="w-4 h-4 mr-2" />
          Add Part
        </Button>
      </div>

      {usedParts && usedParts.length > 0 && (
        <div className="space-y-2">
          {usedParts.map((part, index) => (
            <div key={index} className="flex items-center justify-between bg-gray-50 p-2 rounded">
              <div className="flex-1">
                <span className="font-medium">{part.item_name || part.name} x{part.quantity}</span>
                <span className="text-sm text-gray-600 ml-2">₹{part.price} each</span>
                {(part.manufacturer || part.model || part.serialNo || part.serial_no) && (
                  <div className="text-xs text-gray-600 mt-1">
                    {part.manufacturer && `Manufacturer: ${part.manufacturer} `}
                    {part.model && `Model: ${part.model} `}
                    {(part.serialNo || part.serial_no) && `S/N: ${part.serialNo || part.serial_no}`}
                  </div>
                )}
              </div>
              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={() => onRemovePart(index)}
              >
                <X className="w-4 h-4" />
              </Button>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
