
import { useState } from "react";
import { Card, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Plus, Search, Edit, Mail, MoreHorizontal, Eye, Trash2, ClipboardList } from "lucide-react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { CustomerDialog } from "@/components/dialogs/CustomerDialog";
import { CustomerReadOnlyDialog } from "@/components/dialogs/CustomerReadOnlyDialog";
import { InvoiceReadOnlyDialog } from "@/components/dialogs/InvoiceReadOnlyDialog";
import { JobsheetDialog } from "@/components/dialogs/JobsheetDialog";
import { useToast } from "@/hooks/use-toast";
import { useAppData } from "@/contexts/AppDataContext";

export default function Customers() {
  const { customers, invoices, jobsheets, addCustomer, updateCustomer, deleteCustomer, addJobsheet } = useAppData();
  const [searchTerm, setSearchTerm] = useState("");
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false);
  const [isInvoiceViewOpen, setIsInvoiceViewOpen] = useState(false);
  const [isJobsheetDialogOpen, setIsJobsheetDialogOpen] = useState(false);
  const [editingCustomer, setEditingCustomer] = useState(undefined);
  const [viewingCustomer, setViewingCustomer] = useState(undefined);
  const [viewingInvoice, setViewingInvoice] = useState(undefined);
  const [selectedCustomerForJobsheet, setSelectedCustomerForJobsheet] = useState(undefined);
  const { toast } = useToast();

  const filteredCustomers = customers.filter(customer =>
    customer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    customer.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
    customer.phone.includes(searchTerm)
  );

  const handleAddCustomer = () => {
    setEditingCustomer(undefined);
    setIsDialogOpen(true);
  };

  const handleEditCustomer = (customer: any) => {
    setEditingCustomer(customer);
    setIsDialogOpen(true);
  };

  const handleViewCustomer = (customer: any) => {
    setViewingCustomer(customer);
    setIsViewDialogOpen(true);
  };

  const handleDeleteCustomer = (customerId: string) => {
    deleteCustomer(customerId);
    toast({
      title: "Customer Deleted",
      description: "Customer has been successfully deleted.",
    });
  };

  const handleViewInvoice = (invoiceId: string) => {
    const invoice = invoices.find(inv => inv.id === invoiceId);
    if (invoice) {
      setViewingInvoice(invoice);
      setIsInvoiceViewOpen(true);
    }
  };

  const handleSaveCustomer = (customerData: any) => {
    if (editingCustomer) {
      updateCustomer(editingCustomer.id, customerData);
      toast({
        title: "Customer Updated",
        description: "Customer information has been successfully updated.",
      });
    } else {
      addCustomer(customerData);
      toast({
        title: "Customer Added",
        description: "New customer has been successfully added.",
      });
    }
  };

  const handleCreateJobsheet = (customer: any) => {
    setSelectedCustomerForJobsheet(customer);
    setIsJobsheetDialogOpen(true);
  };

  const handleSaveJobsheet = (jobsheetData: any) => {
    addJobsheet(jobsheetData);
    toast({
      title: "Jobsheet Created",
      description: "Jobsheet has been successfully created.",
    });
  };

  const getCustomerStats = (customerName: string) => {
    const customerInvoices = invoices.filter(inv => inv.customer === customerName);
    
    // Helper function to safely convert amount to number
    const getNumericAmount = (amount: string | number): number => {
      if (typeof amount === 'number') return amount;
      return parseFloat(amount.replace(/[₹,]/g, '') || '0');
    };

    const totalSpent = customerInvoices
      .filter(inv => inv.status === 'Paid')
      .reduce((sum, inv) => sum + getNumericAmount(inv.amount), 0);
    
    return {
      purchases: customerInvoices.filter(inv => inv.status === 'Paid').length,
      services: customerInvoices.length,
      totalSpent: `₹${totalSpent.toLocaleString()}`
    };
  };

  const getInitials = (name: string) => {
    return name.split(' ').map(n => n[0]).join('');
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-gray-900">Customer Management</h1>
        <Button onClick={handleAddCustomer} className="bg-orange-500 hover:bg-orange-600 text-white">
          <Plus className="w-4 h-4 mr-2" />
          Add Customer
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="text-lg font-semibold text-gray-900">Customers</CardTitle>
          <p className="text-sm text-gray-600">Manage customer information and history</p>
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-4 mb-6">
            <div className="relative flex-1 max-w-sm">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="Search customers..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>

          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Customer</TableHead>
                <TableHead>Contact</TableHead>
                <TableHead>Address</TableHead>
                <TableHead>Purchases</TableHead>
                <TableHead>Services</TableHead>
                <TableHead>Total Spent</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredCustomers.map((customer) => {
                const stats = getCustomerStats(customer.name);
                return (
                  <TableRow key={customer.id}>
                    <TableCell>
                      <div className="flex items-center space-x-3">
                        <div className="w-10 h-10 bg-orange-100 rounded-full flex items-center justify-center">
                          <span className="text-orange-600 font-medium text-sm">
                            {getInitials(customer.name)}
                          </span>
                        </div>
                        <div>
                          <div className="font-medium">{customer.name}</div>
                          <div className="text-sm text-gray-500">{customer.email}</div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div>
                        <div className="text-sm">{customer.phone}</div>
                        {customer.alternatePhone && (
                          <div className="text-sm text-gray-500">{customer.alternatePhone}</div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="text-sm">
                        <div>{customer.address}</div>
                        {(customer.city || customer.state) && (
                          <div className="text-gray-500">
                            {customer.city && customer.state ? `${customer.city}, ${customer.state}` : 
                             customer.city || customer.state}
                            {customer.pincode && ` - ${customer.pincode}`}
                          </div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <span className="inline-flex px-2 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800">
                        {stats.purchases}
                      </span>
                    </TableCell>
                    <TableCell>
                      <span className="inline-flex px-2 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800">
                        {stats.services}
                      </span>
                    </TableCell>
                    <TableCell className="font-medium">{stats.totalSpent}</TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-2">
                        <Button variant="ghost" size="sm" onClick={() => handleViewCustomer(customer)}>
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="sm" onClick={() => handleEditCustomer(customer)}>
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button 
                          variant="ghost" 
                          size="sm" 
                          onClick={() => handleCreateJobsheet(customer)}
                          className="text-orange-600 hover:text-orange-700"
                        >
                          <ClipboardList className="h-4 w-4" />
                        </Button>
                        <Button 
                          variant="ghost" 
                          size="sm" 
                          onClick={() => handleDeleteCustomer(customer.id)}
                          className="text-red-600 hover:text-red-700"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      <CustomerDialog
        isOpen={isDialogOpen}
        onClose={() => setIsDialogOpen(false)}
        customer={editingCustomer}
        onSave={handleSaveCustomer}
      />

      <CustomerReadOnlyDialog
        isOpen={isViewDialogOpen}
        onClose={() => {
          setIsViewDialogOpen(false);
          setViewingCustomer(undefined);
        }}
        customer={viewingCustomer}
        onViewInvoice={handleViewInvoice}
      />

      <InvoiceReadOnlyDialog
        isOpen={isInvoiceViewOpen}
        onClose={() => {
          setIsInvoiceViewOpen(false);
          setViewingInvoice(undefined);
        }}
        invoice={viewingInvoice}
      />

      <JobsheetDialog
        isOpen={isJobsheetDialogOpen}
        onClose={() => {
          setIsJobsheetDialogOpen(false);
          setSelectedCustomerForJobsheet(undefined);
        }}
        onSave={handleSaveJobsheet}
        customerData={selectedCustomerForJobsheet ? {
          customer_name: selectedCustomerForJobsheet.name,
          phone: selectedCustomerForJobsheet.phone,
          email: selectedCustomerForJobsheet.email || '',
          address: selectedCustomerForJobsheet.address || '',
          alternate_phone: selectedCustomerForJobsheet.alternatePhone || selectedCustomerForJobsheet.alternate_phone || ''
        } : undefined}
      />
    </div>
  );
}
