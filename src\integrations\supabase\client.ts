
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const supabaseUrl = 'https://sxvoqmiozrsvvspausbu.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InN4dm9xbWlvenJzdnZzcGF1c2J1Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA2NjI1NTksImV4cCI6MjA2NjIzODU1OX0.kh8DOn6aHnzLfYdXlVoD3g2aIOanVVXdD8ewz6tmBHc';

export const supabase = createClient<Database>(supabaseUrl, supabaseAnonKey, {
  auth: {
    storage: localStorage,
    persistSession: true,
    autoRefreshToken: true,
  }
});
