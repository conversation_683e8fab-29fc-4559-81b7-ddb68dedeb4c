
import { useState } from 'react';
import { validateInvoiceData, validateExpenseItem, validateUsedPart, ValidationError } from '@/utils/invoiceValidation';
import type { Invoice, InvoiceExpenseItem, UsedPart } from '@/types';

export function useInvoiceValidation() {
  const [validationErrors, setValidationErrors] = useState<ValidationError[]>([]);

  const validateInvoice = (formData: Invoice): boolean => {
    const result = validateInvoiceData(formData);
    setValidationErrors(result.errors);
    return result.isValid;
  };

  const validateExpenses = (expenses: InvoiceExpenseItem[]): boolean => {
    const allErrors: ValidationError[] = [];
    
    expenses.forEach((expense, index) => {
      const errors = validateExpenseItem(expense);
      errors.forEach(error => {
        allErrors.push({
          field: `expense_${index}_${error.field}`,
          message: `Expense ${index + 1}: ${error.message}`
        });
      });
    });

    setValidationErrors(prev => [...prev, ...allErrors]);
    return allErrors.length === 0;
  };

  const validateParts = (parts: UsedPart[]): boolean => {
    const allErrors: ValidationError[] = [];
    
    parts.forEach((part, index) => {
      const errors = validateUsedPart(part, []);
      errors.forEach(error => {
        allErrors.push({
          field: `part_${index}_${error.field}`,
          message: `Part ${index + 1}: ${error.message}`
        });
      });
    });

    setValidationErrors(prev => [...prev, ...allErrors]);
    return allErrors.length === 0;
  };

  const clearValidationErrors = () => {
    setValidationErrors([]);
  };

  const getFieldError = (fieldName: string): string | undefined => {
    const error = validationErrors.find(err => err.field === fieldName);
    return error?.message;
  };

  return {
    validationErrors,
    validateInvoice,
    validateExpenses,
    validateParts,
    clearValidationErrors,
    getFieldError
  };
}
