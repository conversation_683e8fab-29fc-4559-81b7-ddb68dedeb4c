
import React from "react";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { MessageSquare } from "lucide-react";
import type { Comment } from "../../../types";

interface CommentsSectionProps {
  allComments: Comment[];
  newComment: string;
  setNewComment: (comment: string) => void;
  commentType: 'edit' | 'customer' | 'internal' | 'status_change';
  setCommentType: (type: 'edit' | 'customer' | 'internal' | 'status_change') => void;
}

export function CommentsSection({
  allComments,
  newComment,
  setNewComment,
  commentType,
  setCommentType
}: CommentsSectionProps) {
  return (
    <div className="space-y-4">
      <div className="space-y-3">
        <Label className="flex items-center space-x-2">
          <MessageSquare className="w-4 h-4" />
          <span>Add Comment</span>
        </Label>
        <div className="space-y-2">
          <Select value={commentType} onValueChange={(value: 'edit' | 'customer' | 'internal' | 'status_change') => setCommentType(value)}>
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="internal">Internal</SelectItem>
              <SelectItem value="customer">Customer</SelectItem>
              <SelectItem value="edit">Edit Note</SelectItem>
            </SelectContent>
          </Select>
          <Textarea
            placeholder="Add a comment about this invoice..."
            value={newComment}
            onChange={(e) => setNewComment(e.target.value)}
            rows={3}
          />
        </div>
      </div>
      
      {allComments.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="text-sm">Complete Edit & Status History</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3 max-h-64 overflow-y-auto">
            {allComments.map((comment, index) => (
              <div key={index} className="text-sm border-l-2 border-orange-200 pl-3">
                <div className="flex items-center space-x-2 mb-1">
                  <span className="font-medium text-xs">{comment.author}</span>
                  <Badge variant="outline" className={`text-xs ${
                    comment.type === 'status_change' ? 'bg-blue-50 text-blue-700' :
                    comment.type === 'edit' ? 'bg-green-50 text-green-700' :
                    'bg-gray-50 text-gray-700'
                  }`}>
                    {comment.type === 'status_change' ? 'Status Change' : comment.type}
                  </Badge>
                </div>
                <p className="text-gray-700 text-xs">{comment.text}</p>
                <span className="text-xs text-gray-500">
                  {new Date(comment.timestamp || comment.created_at).toLocaleString()}
                </span>
              </div>
            ))}
          </CardContent>
        </Card>
      )}
    </div>
  );
}
