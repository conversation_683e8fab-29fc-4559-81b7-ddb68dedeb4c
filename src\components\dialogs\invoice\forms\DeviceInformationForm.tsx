
import React from "react";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { ValidationMessage } from "@/components/ui/validation-message";
import type { Invoice } from "../../../../types";

interface DeviceInformationFormProps {
  formData: Invoice;
  setFormData: (data: Invoice) => void;
  validationErrors?: { [key: string]: string };
}

const deviceOptions = [
  "Mobile Phone",
  "Laptop", 
  "Desktop Computer",
  "Tablet",
  "Gaming Console",
  "Smart TV",
  "Router/Modem",
  "Printer",
  "Camera",
  "Headphones/Earphones",
  "Smart Watch",
  "Other"
];

export function DeviceInformationForm({ 
  formData, 
  setFormData, 
  validationErrors = {} 
}: DeviceInformationFormProps) {
  const handleDeviceTypeChange = (value: string) => {
    setFormData({ 
      ...formData, 
      deviceType: value,
      // Clear custom device name if not "Other"
      customDeviceName: value === "Other" ? formData.customDeviceName : ""
    });
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
      <div>
        <Label htmlFor="device">Device Brand/Model *</Label>
        <Input
          id="device"
          value={formData.device || ''}
          onChange={(e) => setFormData({ ...formData, device: e.target.value })}
          placeholder="e.g., iPhone 14, Dell Inspiron"
          className={validationErrors.device ? "border-red-500" : ""}
        />
        {validationErrors.device && (
          <ValidationMessage message={validationErrors.device} />
        )}
      </div>

      <div>
        <Label htmlFor="deviceType">Device Type</Label>
        <Select 
          value={formData.deviceType || ''} 
          onValueChange={handleDeviceTypeChange}
        >
          <SelectTrigger>
            <SelectValue placeholder="Select device type" />
          </SelectTrigger>
          <SelectContent>
            {deviceOptions.map(option => (
              <SelectItem key={option} value={option}>{option}</SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {formData.deviceType === "Other" && (
        <div className="md:col-span-2">
          <Label htmlFor="customDeviceName">Custom Device Name</Label>
          <Input
            id="customDeviceName"
            value={formData.customDeviceName || ''}
            onChange={(e) => setFormData({ ...formData, customDeviceName: e.target.value })}
            placeholder="Enter custom device name"
          />
        </div>
      )}

      <div className="md:col-span-2">
        <Label htmlFor="issue">Issue Description *</Label>
        <Textarea
          id="issue"
          value={formData.issue || ''}
          onChange={(e) => setFormData({ ...formData, issue: e.target.value })}
          placeholder="Describe the issue in detail..."
          rows={3}
          className={validationErrors.issue ? "border-red-500" : ""}
        />
        {validationErrors.issue && (
          <ValidationMessage message={validationErrors.issue} />
        )}
      </div>
    </div>
  );
}
