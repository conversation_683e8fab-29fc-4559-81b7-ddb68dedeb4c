
import { useState, useEffect } from "react";
import type { Invoice, UsedPart, InvoiceExpenseItem } from "../../../types";

export function useInvoiceFormLogic(invoice?: Invoice, isOpen?: boolean) {
  const [formData, setFormData] = useState<Invoice>({
    id: "",
    customer: "",
    date: new Date().toISOString().split('T')[0],
    amount: "",
    status: "Pending",
    device: "",
    deviceType: "",
    customDeviceName: "",
    issue: "",
    gst: "",
    phone: "",
    alternatePhone: "",
    address: "",
    state: "",
    city: "",
    pincode: "",
    estimatedAmount: "",
    billableWarranty: "",
    showRemarks: false,
    remarks: "",
    expectedDelivery: "",
    inspectionFee: "500",
    usedParts: [],
    expenses: [],
    comments: [],
    user_id: ""
  });

  const [originalData, setOriginalData] = useState<Invoice | null>(null);
  const [newComment, setNewComment] = useState("");
  const [commentType, setCommentType] = useState<'edit' | 'customer' | 'internal' | 'status_change'>('internal');

  // Reset form data when dialog opens/closes or invoice changes
  useEffect(() => {
    if (isOpen) {
      if (invoice) {
        console.log('Setting form data from invoice:', invoice);
        setFormData({
          ...invoice,
          customer: invoice.customer || "",
          date: invoice.date || new Date().toISOString().split('T')[0],
          amount: invoice.amount || "",
          status: invoice.status || "Pending",
          device: invoice.device || "",
          deviceType: invoice.deviceType || invoice.device_type || "",
          customDeviceName: invoice.customDeviceName || invoice.custom_device_name || "",
          issue: invoice.issue || "",
          gst: invoice.gst || "",
          phone: invoice.phone || "",
          alternatePhone: invoice.alternatePhone || invoice.alternate_phone || "",
          address: invoice.address || "",
          state: invoice.state || "",
          city: invoice.city || "",
          pincode: invoice.pincode || "",
          estimatedAmount: invoice.estimatedAmount || invoice.estimated_amount || "",
          billableWarranty: invoice.billableWarranty || invoice.billable_warranty || "",
          showRemarks: invoice.showRemarks || invoice.show_remarks || false,
          remarks: invoice.remarks || "",
          expectedDelivery: invoice.expectedDelivery || invoice.expected_delivery || "",
          inspectionFee: invoice.inspectionFee || invoice.inspection_fee || "500",
          usedParts: invoice.usedParts || [],
          expenses: invoice.expenses || [],
          comments: invoice.comments || []
        });
        setOriginalData(JSON.parse(JSON.stringify(invoice)));
      } else {
        // Calculate expected delivery for new invoice
        const billDate = new Date().toISOString().split('T')[0];
        const deliveryDate = new Date();
        deliveryDate.setDate(deliveryDate.getDate() + 3);
        
        setFormData({
          id: "",
          customer: "",
          date: billDate,
          amount: "",
          status: "Pending",
          device: "",
          deviceType: "",
          customDeviceName: "",
          issue: "",
          gst: "",
          phone: "",
          alternatePhone: "",
          address: "",
          state: "",
          city: "",
          pincode: "",
          estimatedAmount: "",
          billableWarranty: "",
          showRemarks: false,
          remarks: "",
          expectedDelivery: deliveryDate.toISOString().split('T')[0],
          inspectionFee: "500",
          usedParts: [],
          expenses: [],
          comments: [],
          user_id: ""
        });
        setOriginalData(null);
      }
      // Reset comment form
      setNewComment("");
      setCommentType('internal');
    }
  }, [isOpen, invoice]);

  const handleUpdateParts = (parts: UsedPart[]) => {
    setFormData(prev => ({ ...prev, usedParts: parts }));
  };

  const handleUpdateExpenses = (expenses: InvoiceExpenseItem[]) => {
    setFormData(prev => ({ ...prev, expenses }));
  };

  return {
    formData,
    setFormData,
    originalData,
    newComment,
    setNewComment,
    commentType,
    setCommentType,
    handleUpdateParts,
    handleUpdateExpenses
  };
}
