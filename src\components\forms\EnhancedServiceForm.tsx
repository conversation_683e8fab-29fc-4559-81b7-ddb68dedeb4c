
import React from "react";
import { Button } from "@/components/ui/button";
import { format } from "date-fns";
import { ServiceBasicInfo } from "./service/ServiceBasicInfo";
import { ServiceDetailsSection } from "./service/ServiceDetailsSection";
import { ServiceCostBreakdown } from "./service/ServiceCostBreakdown";
import type { Service } from "../../types";

interface EnhancedServiceFormProps {
  formData: Partial<Service>;
  setFormData: (data: Partial<Service>) => void;
  onSubmit: (e: React.FormEvent) => void;
  onCancel: () => void;
  isLoading?: boolean;
}

export function EnhancedServiceForm({ 
  formData, 
  setFormData, 
  onSubmit, 
  onCancel,
  isLoading = false 
}: EnhancedServiceFormProps) {
  const handleInputChange = (field: keyof Service, value: string | number) => {
    setFormData({ ...formData, [field]: value });
  };

  const handleDateChange = (field: keyof Service, date: Date | undefined) => {
    if (date) {
      setFormData({ ...formData, [field]: format(date, 'yyyy-MM-dd') });
    }
  };

  return (
    <form onSubmit={onSubmit} className="space-y-6">
      <ServiceBasicInfo formData={formData} handleInputChange={handleInputChange} />
      <ServiceDetailsSection 
        formData={formData} 
        handleInputChange={handleInputChange}
        handleDateChange={handleDateChange}
      />
      <ServiceCostBreakdown formData={formData} handleInputChange={handleInputChange} />

      <div className="flex justify-end space-x-4">
        <Button type="button" variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button type="submit" disabled={isLoading}>
          {isLoading ? "Saving..." : "Save Service"}
        </Button>
      </div>
    </form>
  );
}
