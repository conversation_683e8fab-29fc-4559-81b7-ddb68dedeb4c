
import React from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>hart, Pie, Cell, ResponsiveContainer, BarChart, Bar, XAxis, YAxis, Tooltip } from "recharts";
import type { Invoice, Expense } from "../../types";

interface ProfitLossReportProps {
  invoices: Invoice[];
  expenses: Expense[];
}

export function ProfitLossReport({ invoices, expenses }: ProfitLossReportProps) {
  const totalRevenue = invoices
    .filter(invoice => invoice.status === 'Paid')
    .reduce((sum, invoice) => sum + parseFloat(invoice.amount.replace(/[₹,]/g, '') || '0'), 0);

  const totalExpenses = expenses
    .reduce((sum, expense) => sum + parseFloat(expense.amount.replace(/[₹,]/g, '') || '0'), 0);

  const profit = totalRevenue - totalExpenses;

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-3 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="text-green-600">Revenue</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">₹{totalRevenue.toLocaleString()}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader>
            <CardTitle className="text-red-600">Expenses</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">₹{totalExpenses.toLocaleString()}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader>
            <CardTitle className={profit >= 0 ? "text-green-600" : "text-red-600"}>
              {profit >= 0 ? "Profit" : "Loss"}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">₹{Math.abs(profit).toLocaleString()}</div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
