
import type { Invoice, UsedPart, InvoiceExpenseItem, Comment } from '@/types';

export const transformDatabaseInvoice = (dbInvoice: any): Invoice => {
  return {
    id: dbInvoice.id,
    customer: dbInvoice.customer_name,
    date: dbInvoice.date,
    amount: `₹${dbInvoice.amount?.toLocaleString() || '0'}`,
    status: dbInvoice.status,
    device: dbInvoice.device,
    deviceType: dbInvoice.device_type,
    customDeviceName: dbInvoice.custom_device_name,
    issue: dbInvoice.issue,
    phone: dbInvoice.phone,
    alternatePhone: dbInvoice.alternate_phone,
    email: dbInvoice.email,
    address: dbInvoice.address,
    city: dbInvoice.city,
    state: dbInvoice.state,
    pincode: dbInvoice.pincode,
    expectedDelivery: dbInvoice.expected_delivery,
    inspectionFee: dbInvoice.inspection_fee?.toString() || '500',
    billableWarranty: dbInvoice.billable_warranty,
    remarks: dbInvoice.remarks,
    showRemarks: dbInvoice.show_remarks,
    gst: dbInvoice.gst?.toString() || '',
    estimatedAmount: dbInvoice.estimated_amount?.toString() || '',
    user_id: dbInvoice.user_id,
    usedParts: [],
    expenses: [],
    comments: []
  };
};

export const transformDatabaseUsedPart = (dbPart: any): UsedPart => {
  return {
    id: dbPart.id,
    itemId: dbPart.item_id,
    item_id: dbPart.item_id,
    item_name: dbPart.item_name,
    quantity: dbPart.quantity,
    price: dbPart.price || 0,
    manufacturer: dbPart.manufacturer,
    model: dbPart.model,
    serialNo: dbPart.serial_no,
    serial_no: dbPart.serial_no,
    is_custom: dbPart.is_custom || false,
    invoice_id: dbPart.invoice_id
  };
};

export const transformDatabaseExpenseItem = (dbExpense: any): InvoiceExpenseItem => {
  return {
    description: dbExpense.description,
    amount: `₹${dbExpense.amount?.toLocaleString() || '0'}`,
    category: dbExpense.category
  };
};

export const transformDatabaseComment = (dbComment: any): Comment => {
  return {
    id: dbComment.id,
    text: dbComment.text,
    author: dbComment.author,
    type: dbComment.type,
    created_at: dbComment.created_at,
    timestamp: dbComment.created_at,
    service_id: dbComment.service_id,
    invoice_id: dbComment.invoice_id
  };
};
