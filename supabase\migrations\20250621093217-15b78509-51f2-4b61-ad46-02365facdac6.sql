
-- Add email column to customers table if it doesn't exist
ALTER TABLE public.customers 
ADD COLUMN IF NOT EXISTS email text;

-- Add email column to invoices table if it doesn't exist  
ALTER TABLE public.invoices
ADD COLUMN IF NOT EXISTS email text;

-- Update the used_parts table to better track custom parts
ALTER TABLE public.used_parts 
ADD COLUMN IF NOT EXISTS unit_price numeric DEFAULT 0,
ADD COLUMN IF NOT EXISTS total_price numeric DEFAULT 0;

-- Add trigger to automatically update inventory when parts are used
CREATE OR REPLACE FUNCTION update_inventory_stock()
R<PERSON><PERSON><PERSON> trigger AS $$
BEGIN
  IF TG_OP = 'INSERT' THEN
    -- Reduce inventory when part is used (only for non-custom parts)
    IF NEW.item_id IS NOT NULL AND NOT COALESCE(NEW.is_custom, false) THEN
      UPDATE public.inventory_items 
      SET 
        current_stock = current_stock - NEW.quantity,
        total_sales = COALESCE(total_sales, 0) + NEW.quantity,
        updated_at = NOW()
      WHERE id = NEW.item_id;
    END IF;
    RETURN NEW;
  END IF;
  RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- <PERSON>reate trigger for inventory updates
DROP TRIGGER IF EXISTS trigger_update_inventory_stock ON public.used_parts;
CREATE TRIGGER trigger_update_inventory_stock
  AFTER INSERT ON public.used_parts
  FOR EACH ROW EXECUTE FUNCTION update_inventory_stock();

-- Add sold_today tracking function
CREATE OR REPLACE FUNCTION reset_daily_sales()
RETURNS void AS $$
BEGIN
  UPDATE public.inventory_items 
  SET sold_today = 0 
  WHERE sold_today > 0;
END;
$$ LANGUAGE plpgsql;
