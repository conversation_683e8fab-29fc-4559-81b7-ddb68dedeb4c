
import React from "react";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import type { Service } from "../../../types";

interface ServiceBasicInfoProps {
  formData: Partial<Service>;
  handleInputChange: (field: keyof Service, value: string | number) => void;
}

export function ServiceBasicInfo({ formData, handleInputChange }: ServiceBasicInfoProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg">Service Information</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium mb-1">Customer Name *</label>
            <Input
              value={formData.customer || ""}
              onChange={(e) => handleInputChange("customer", e.target.value)}
              required
            />
          </div>
          <div>
            <label className="block text-sm font-medium mb-1">Phone *</label>
            <Input
              value={formData.phone || ""}
              onChange={(e) => handleInputChange("phone", e.target.value)}
              required
            />
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium mb-1">Device *</label>
            <Input
              value={formData.device || ""}
              onChange={(e) => handleInputChange("device", e.target.value)}
              required
            />
          </div>
          <div>
            <label className="block text-sm font-medium mb-1">Service Type</label>
            <Select
              value={formData.service_type || "Repair"}
              onValueChange={(value) => handleInputChange("service_type", value)}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="Repair">Repair</SelectItem>
                <SelectItem value="Maintenance">Maintenance</SelectItem>
                <SelectItem value="Installation">Installation</SelectItem>
                <SelectItem value="Consultation">Consultation</SelectItem>
                <SelectItem value="Upgrade">Upgrade</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium mb-1">Issue Description *</label>
          <Textarea
            value={formData.issue || ""}
            onChange={(e) => handleInputChange("issue", e.target.value)}
            required
          />
        </div>
      </CardContent>
    </Card>
  );
}
