
import React from 'react';
import { AlertCircle } from 'lucide-react';

interface ValidationMessageProps {
  message: string;
  className?: string;
}

export function ValidationMessage({ message, className = '' }: ValidationMessageProps) {
  return (
    <div className={`flex items-center gap-2 text-sm text-red-600 mt-1 ${className}`}>
      <AlertCircle className="w-4 h-4" />
      <span>{message}</span>
    </div>
  );
}
