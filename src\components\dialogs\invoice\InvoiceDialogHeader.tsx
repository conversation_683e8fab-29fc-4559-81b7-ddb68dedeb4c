
import React from "react";
import { <PERSON><PERSON>Header, DialogTitle } from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import type { Invoice } from "../../../types";

interface InvoiceDialogHeaderProps {
  invoice?: Invoice;
}

export function InvoiceDialogHeader({ invoice }: InvoiceDialogHeaderProps) {
  return (
    <DialogHeader>
      <DialogTitle className="flex items-center justify-between">
        <span>{invoice ? "Edit Invoice" : "Create New Invoice"}</span>
        <div className="flex items-center space-x-2">
          {invoice && (
            <Badge variant={
              invoice.status === 'Paid' ? 'default' :
              invoice.status === 'Pending' ? 'secondary' :
              invoice.status === 'Overdue' ? 'destructive' :
              'outline'
            }>
              {invoice.status}
            </Badge>
          )}
        </div>
      </DialogTitle>
    </DialogHeader>
  );
}
