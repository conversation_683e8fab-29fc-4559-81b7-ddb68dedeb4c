
import React from "react";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { ValidationMessage } from "@/components/ui/validation-message";
import type { Invoice } from "../../../../types";

interface BillingInformationFormProps {
  formData: Invoice;
  setFormData: (data: Invoice) => void;
  calculateExpectedDelivery: (billDate: string) => string;
  validationErrors?: { [key: string]: string };
}

export function BillingInformationForm({ 
  formData, 
  setFormData, 
  calculateExpectedDelivery,
  validationErrors = {}
}: BillingInformationFormProps) {
  const handleDateChange = (date: string) => {
    const expectedDelivery = calculateExpectedDelivery(date);
    setFormData({ 
      ...formData, 
      date,
      expectedDelivery 
    });
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
      <div>
        <Label htmlFor="date">Service Date *</Label>
        <Input
          id="date"
          type="date"
          value={formData.date}
          onChange={(e) => handleDateChange(e.target.value)}
          className={validationErrors.date ? "border-red-500" : ""}
        />
        {validationErrors.date && (
          <ValidationMessage message={validationErrors.date} />
        )}
      </div>

      <div>
        <Label htmlFor="expectedDelivery">Expected Delivery</Label>
        <Input
          id="expectedDelivery"
          type="date"
          value={formData.expectedDelivery || ''}
          onChange={(e) => setFormData({ ...formData, expectedDelivery: e.target.value })}
        />
      </div>

      <div>
        <Label htmlFor="amount">Total Amount *</Label>
        <Input
          id="amount"
          value={formData.amount}
          onChange={(e) => setFormData({ ...formData, amount: e.target.value })}
          placeholder="Enter total amount"
          className={validationErrors.amount ? "border-red-500" : ""}
        />
        {validationErrors.amount && (
          <ValidationMessage message={validationErrors.amount} />
        )}
      </div>

      <div>
        <Label htmlFor="estimatedAmount">Estimated Amount</Label>
        <Input
          id="estimatedAmount"
          value={formData.estimatedAmount || ''}
          onChange={(e) => setFormData({ ...formData, estimatedAmount: e.target.value })}
          placeholder="Enter estimated amount"
        />
      </div>

      <div>
        <Label htmlFor="inspectionFee">Inspection Fee</Label>
        <Input
          id="inspectionFee"
          value={formData.inspectionFee}
          onChange={(e) => setFormData({ ...formData, inspectionFee: e.target.value })}
          placeholder="Enter inspection fee"
        />
      </div>

      <div>
        <Label htmlFor="gst">GST (%)</Label>
        <Input
          id="gst"
          value={formData.gst || ''}
          onChange={(e) => setFormData({ ...formData, gst: e.target.value })}
          placeholder="Enter GST percentage"
          className={validationErrors.gst ? "border-red-500" : ""}
        />
        {validationErrors.gst && (
          <ValidationMessage message={validationErrors.gst} />
        )}
      </div>

      <div>
        <Label htmlFor="status">Status</Label>
        <Select 
          value={formData.status} 
          onValueChange={(value: "Draft" | "Pending" | "Paid" | "Overdue" | "Cancelled") => 
            setFormData({ ...formData, status: value })
          }
        >
          <SelectTrigger>
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="Draft">Draft</SelectItem>
            <SelectItem value="Pending">Pending</SelectItem>
            <SelectItem value="Paid">Paid</SelectItem>
            <SelectItem value="Overdue">Overdue</SelectItem>
            <SelectItem value="Cancelled">Cancelled</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div>
        <Label htmlFor="billableWarranty">Billable Warranty</Label>
        <Input
          id="billableWarranty"
          value={formData.billableWarranty || ''}
          onChange={(e) => setFormData({ ...formData, billableWarranty: e.target.value })}
          placeholder="Enter warranty details"
        />
      </div>

      <div className="md:col-span-2 flex items-center space-x-2">
        <Switch
          id="showRemarks"
          checked={formData.showRemarks}
          onCheckedChange={(checked) => setFormData({ ...formData, showRemarks: checked })}
        />
        <Label htmlFor="showRemarks">Show Remarks on Invoice</Label>
      </div>

      {formData.showRemarks && (
        <div className="md:col-span-2">
          <Label htmlFor="remarks">Remarks</Label>
          <Textarea
            id="remarks"
            value={formData.remarks || ''}
            onChange={(e) => setFormData({ ...formData, remarks: e.target.value })}
            placeholder="Enter remarks..."
            rows={3}
          />
        </div>
      )}
    </div>
  );
}
