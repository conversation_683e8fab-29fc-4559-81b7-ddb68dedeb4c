
export interface Customer {
  id: string;
  name: string;
  phone: string;
  email?: string;
  address: string;
  city?: string;
  state?: string;
  pincode?: string;
  alternatePhone?: string;
  alternate_phone?: string;
  warranty?: string;
  purchases?: number;
  services?: number;
  totalSpent?: string;
  total_spent?: number;
  customerType?: string;
  customer_type?: string;
  creditLimit?: number;
  credit_limit?: number;
  paymentTerms?: string;
  payment_terms?: string;
  taxId?: string;
  tax_id?: string;
  preferredContact?: string;
  preferred_contact?: string;
  notes?: string;
  lastServiceDate?: string;
  last_service_date?: string;
  user_id: string;
}
