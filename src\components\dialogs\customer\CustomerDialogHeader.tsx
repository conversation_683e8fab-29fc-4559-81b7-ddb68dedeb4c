
import { DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { User } from "lucide-react";
import type { Customer } from "../../../types";

interface CustomerDialogHeaderProps {
  customer?: Customer;
}

export function CustomerDialogHeader({ customer }: CustomerDialogHeaderProps) {
  return (
    <DialogHeader>
      <DialogTitle className="flex items-center space-x-2">
        <User className="w-5 h-5" />
        <span>{customer ? 'Edit Customer' : 'Add New Customer'}</span>
      </DialogTitle>
    </DialogHeader>
  );
}
