
import type { Invoice, Comment } from "../../../types";

export function useInvoiceSubmitLogic() {
  const createSubmitHandler = (
    formData: Invoice,
    originalData: Invoice | null,
    newComment: string,
    commentType: 'edit' | 'customer' | 'internal' | 'status_change',
    addExpense: (expense: any) => void,
    onSave: (invoice: Invoice) => void,
    onClose: () => void,
    invoice?: Invoice
  ) => {
    return (e: React.FormEvent) => {
      e.preventDefault();
      
      // Track what changes were made
      const changes: string[] = [];
      if (originalData) {
        if (originalData.amount !== formData.amount) {
          changes.push(`Amount changed from ${originalData.amount} to ${formData.amount}`);
        }
        if (originalData.status !== formData.status) {
          changes.push(`Status changed from ${originalData.status} to ${formData.status}`);
        }
        if (originalData.device !== formData.device) {
          changes.push(`Device updated to ${formData.device}`);
        }
        if (originalData.issue !== formData.issue) {
          changes.push(`Issue description updated`);
        }
      }

      // Add automatic edit comment if changes were made
      const updatedComments = [...(formData.comments || [])];
      if (changes.length > 0) {
        updatedComments.push({
          id: `comment-${Date.now()}-1`,
          text: `Invoice updated: ${changes.join(', ')}`,
          author: 'System',
          created_at: new Date().toISOString(),
          timestamp: new Date().toISOString(),
          type: 'edit'
        });
      }

      // Add "Invoice created" comment for new invoices
      if (!originalData) {
        updatedComments.push({
          id: `comment-${Date.now()}-2`,
          text: 'Invoice created',
          author: 'System',
          created_at: new Date().toISOString(),
          timestamp: new Date().toISOString(),
          type: 'edit'
        });
      }

      // Add manual comment if provided
      if (newComment.trim()) {
        updatedComments.push({
          id: `comment-${Date.now()}-3`,
          text: newComment.trim(),
          author: 'Current User',
          created_at: new Date().toISOString(),
          timestamp: new Date().toISOString(),
          type: commentType
        });
      }

      const finalData = {
        ...formData,
        comments: updatedComments
      };

      // Add ALL expenses to the expense tracker
      formData.expenses?.forEach(expense => {
        const numericAmount = parseFloat(expense.amount.replace(/[₹,]/g, '')) || 0;
        addExpense({
          date: formData.date,
          description: expense.description,
          category: expense.category,
          amount: `₹${numericAmount.toLocaleString()}`,
          vendor: "Internal",
          receipt: "Available",
          invoice_id: invoice?.id || `INV-${Date.now()}`
        });
      });

      onSave(finalData);
      onClose();
    };
  };

  return { createSubmitHandler };
}
