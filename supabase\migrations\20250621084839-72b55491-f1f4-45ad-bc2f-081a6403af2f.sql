
-- Drop existing policies first to avoid conflicts
DROP POLICY IF EXISTS "Users can manage their own customers" ON public.customers;
DROP POLICY IF EXISTS "Users can manage their own services" ON public.services;
DROP POLICY IF EXISTS "Users can manage their own invoices" ON public.invoices;
DROP POLICY IF EXISTS "Users can manage their own inventory" ON public.inventory_items;
DROP POLICY IF EXISTS "Users can manage their own expenses" ON public.expenses;
DROP POLICY IF EXISTS "Users can manage used parts for their invoices" ON public.used_parts;
DROP POLICY IF EXISTS "Users can manage comments for their content" ON public.comments;
DROP POLICY IF EXISTS "Users can manage expense items for their invoices" ON public.expense_items;

-- First, clean up existing data that doesn't have user_id (if any remain)
DELETE FROM public.used_parts WHERE invoice_id IN (SELECT id FROM public.invoices WHERE user_id IS NULL);
DELETE FROM public.comments WHERE service_id IN (SELECT id FROM public.services WHERE user_id IS NULL) OR invoice_id IN (SELECT id FROM public.invoices WHERE user_id IS NULL);
DELETE FROM public.expense_items WHERE invoice_id IN (SELECT id FROM public.invoices WHERE user_id IS NULL);
DELETE FROM public.expenses WHERE user_id IS NULL;
DELETE FROM public.invoices WHERE user_id IS NULL;
DELETE FROM public.services WHERE user_id IS NULL;
DELETE FROM public.inventory_items WHERE user_id IS NULL;
DELETE FROM public.customers WHERE user_id IS NULL;

-- Make user_id columns NOT NULL (skip if already set)
DO $$ 
BEGIN
    -- Check and set NOT NULL for customers
    IF EXISTS(SELECT 1 FROM information_schema.columns WHERE table_name='customers' AND column_name='user_id' AND is_nullable='YES') THEN
        ALTER TABLE public.customers ALTER COLUMN user_id SET NOT NULL;
    END IF;
    
    -- Check and set NOT NULL for services
    IF EXISTS(SELECT 1 FROM information_schema.columns WHERE table_name='services' AND column_name='user_id' AND is_nullable='YES') THEN
        ALTER TABLE public.services ALTER COLUMN user_id SET NOT NULL;
    END IF;
    
    -- Check and set NOT NULL for invoices
    IF EXISTS(SELECT 1 FROM information_schema.columns WHERE table_name='invoices' AND column_name='user_id' AND is_nullable='YES') THEN
        ALTER TABLE public.invoices ALTER COLUMN user_id SET NOT NULL;
    END IF;
    
    -- Check and set NOT NULL for inventory_items
    IF EXISTS(SELECT 1 FROM information_schema.columns WHERE table_name='inventory_items' AND column_name='user_id' AND is_nullable='YES') THEN
        ALTER TABLE public.inventory_items ALTER COLUMN user_id SET NOT NULL;
    END IF;
    
    -- Check and set NOT NULL for expenses
    IF EXISTS(SELECT 1 FROM information_schema.columns WHERE table_name='expenses' AND column_name='user_id' AND is_nullable='YES') THEN
        ALTER TABLE public.expenses ALTER COLUMN user_id SET NOT NULL;
    END IF;
END $$;

-- Create RLS policies for all tables
CREATE POLICY "Users can manage their own customers" ON public.customers
  FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can manage their own services" ON public.services
  FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can manage their own invoices" ON public.invoices
  FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can manage their own inventory" ON public.inventory_items
  FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can manage their own expenses" ON public.expenses
  FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can manage used parts for their invoices" ON public.used_parts
  FOR ALL USING (EXISTS (
    SELECT 1 FROM public.invoices 
    WHERE invoices.id = used_parts.invoice_id 
    AND invoices.user_id = auth.uid()
  ));

CREATE POLICY "Users can manage comments for their content" ON public.comments
  FOR ALL USING (
    (service_id IS NOT NULL AND EXISTS (
      SELECT 1 FROM public.services 
      WHERE services.id = comments.service_id 
      AND services.user_id = auth.uid()
    )) OR 
    (invoice_id IS NOT NULL AND EXISTS (
      SELECT 1 FROM public.invoices 
      WHERE invoices.id = comments.invoice_id 
      AND invoices.user_id = auth.uid()
    ))
  );

CREATE POLICY "Users can manage expense items for their invoices" ON public.expense_items
  FOR ALL USING (EXISTS (
    SELECT 1 FROM public.invoices 
    WHERE invoices.id = expense_items.invoice_id 
    AND invoices.user_id = auth.uid()
  ));
