
import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON>Header, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Plus, Search } from "lucide-react";
import { InvoiceDialog } from "@/components/dialogs/InvoiceDialog";
import { InvoiceReadOnlyDialog } from "@/components/dialogs/InvoiceReadOnlyDialog";
import { RevenueStats } from "@/components/billing/RevenueStats";
import { InvoiceTable } from "@/components/billing/InvoiceTable";
import { useInvoiceActions } from "@/components/billing/InvoiceActions";
import { useToast } from "@/hooks/use-toast";
import { useAppData } from "@/contexts/AppDataContext";

export default function Billing() {
  const { invoices, addInvoice, updateInvoice, deleteInvoice } = useAppData();
  const [searchTerm, setSearchTerm] = useState("");
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false);
  const [editingInvoice, setEditingInvoice] = useState(null);
  const [viewingInvoice, setViewingInvoice] = useState(null);
  const { toast } = useToast();
  const { handleMailInvoice, handleDownloadInvoice } = useInvoiceActions();

  const filteredInvoices = invoices.filter(invoice =>
    invoice.customer.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (invoice.id && invoice.id.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  // Helper function to safely convert amount to number
  const getNumericAmount = (amount: string | number): number => {
    if (typeof amount === 'number') return amount;
    return parseFloat(amount.replace(/[₹,]/g, '') || '0');
  };

  const totalRevenue = invoices
    .filter(inv => inv.status === 'Paid')
    .reduce((sum, inv) => sum + getNumericAmount(inv.amount), 0);

  const pendingRevenue = invoices
    .filter(inv => inv.status === 'Pending')
    .reduce((sum, inv) => sum + getNumericAmount(inv.amount), 0);

  const paidInvoices = invoices.filter(inv => inv.status === 'Paid').length;
  const pendingInvoicesCount = invoices.filter(inv => inv.status === 'Pending').length;

  const handleCreateInvoice = () => {
    setEditingInvoice(null);
    setIsDialogOpen(true);
  };

  const handleViewInvoice = (invoice: any) => {
    setViewingInvoice(invoice);
    setIsViewDialogOpen(true);
  };

  const handleEditInvoice = (invoice: any) => {
    setEditingInvoice(invoice);
    setIsDialogOpen(true);
  };

  const handleSaveInvoice = (invoiceData: any) => {
    console.log('Saving invoice:', invoiceData);
    if (editingInvoice) {
      updateInvoice(editingInvoice.id, invoiceData);
      toast({
        title: "Invoice updated",
        description: "The invoice has been successfully updated.",
      });
    } else {
      const newInvoice = addInvoice(invoiceData);
      console.log('New invoice created:', newInvoice);
      toast({
        title: "Invoice created",
        description: "New invoice has been successfully created. Service request and customer data have been automatically generated with the same ID.",
      });
    }
  };

  const handleDeleteInvoice = (id: string) => {
    deleteInvoice(id);
    toast({
      title: "Invoice deleted",
      description: "The invoice has been successfully deleted.",
    });
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-gray-900">Billing & Invoices</h1>
        <Button onClick={handleCreateInvoice} className="bg-orange-500 hover:bg-orange-600 text-white">
          <Plus className="w-4 h-4 mr-2" />
          New Invoice
        </Button>
      </div>

      <RevenueStats
        totalRevenue={totalRevenue}
        pendingRevenue={pendingRevenue}
        totalInvoices={invoices.length}
        paidInvoices={paidInvoices}
        pendingInvoicesCount={pendingInvoicesCount}
      />

      <Card>
        <CardHeader>
          <CardTitle className="text-lg font-semibold text-gray-900">Invoices</CardTitle>
          <p className="text-sm text-gray-600">Manage customer invoices and payments</p>
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-4 mb-6">
            <div className="relative flex-1 max-w-sm">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="Search invoices..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>

          <InvoiceTable
            filteredInvoices={filteredInvoices}
            onViewInvoice={handleViewInvoice}
            onEditInvoice={handleEditInvoice}
            onMailInvoice={handleMailInvoice}
            onDownloadInvoice={handleDownloadInvoice}
            onDeleteInvoice={handleDeleteInvoice}
          />
        </CardContent>
      </Card>

      <InvoiceDialog
        isOpen={isDialogOpen}
        onClose={() => {
          setIsDialogOpen(false);
          setEditingInvoice(null);
        }}
        invoice={editingInvoice}
        onSave={handleSaveInvoice}
      />

      <InvoiceReadOnlyDialog
        isOpen={isViewDialogOpen}
        onClose={() => {
          setIsViewDialogOpen(false);
          setViewingInvoice(null);
        }}
        invoice={viewingInvoice}
      />
    </div>
  );
}
