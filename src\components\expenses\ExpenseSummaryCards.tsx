
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { TrendingUp, TrendingDown } from "lucide-react";
import type { Expense } from "../../types";

interface ExpenseSummaryCardsProps {
  expenses: Expense[];
  categoryData: { name: string; value: number; color: string }[];
}

export function ExpenseSummaryCards({ expenses, categoryData }: ExpenseSummaryCardsProps) {
  const totalExpenses = expenses.reduce((sum, expense) => 
    sum + parseFloat(expense.amount.replace(/[₹,]/g, '')), 0
  );

  return (
    <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium text-gray-600">Total Expenses</CardTitle>
          <TrendingUp className="h-5 w-5 text-orange-500" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-gray-900">
            ₹{totalExpenses.toLocaleString()}
          </div>
          <p className="text-xs text-gray-600 flex items-center mt-1">
            <TrendingUp className="h-3 w-3 mr-1" />
            Current total
          </p>
          <p className="text-xs text-gray-500 mt-1">All expenses</p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium text-gray-600">Equipment Costs</CardTitle>
          <TrendingDown className="h-5 w-5 text-orange-500" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-gray-900">
            ₹{(categoryData.find(c => c.name === 'Equipment')?.value || 0).toLocaleString()}
          </div>
          <p className="text-xs text-green-600 flex items-center mt-1">
            <TrendingDown className="h-3 w-3 mr-1" />
            Equipment category
          </p>
          <p className="text-xs text-gray-500 mt-1">Hardware & tools</p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium text-gray-600">Operating Costs</CardTitle>
          <TrendingUp className="h-5 w-5 text-orange-500" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-gray-900">
            ₹{((categoryData.find(c => c.name === 'Office')?.value || 0) + (categoryData.find(c => c.name === 'Utilities')?.value || 0)).toLocaleString()}
          </div>
          <p className="text-xs text-red-600 flex items-center mt-1">
            <TrendingUp className="h-3 w-3 mr-1" />
            Office & utilities
          </p>
          <p className="text-xs text-gray-500 mt-1">Rent, utilities, etc.</p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium text-gray-600">Pending Receipts</CardTitle>
          <TrendingUp className="h-5 w-5 text-orange-500" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-gray-900">
            {expenses.filter(e => e.receipt === 'Pending').length}
          </div>
          <p className="text-xs text-gray-600 mt-1">
            ₹{expenses.filter(e => e.receipt === 'Pending').reduce((sum, e) => 
              sum + parseFloat(e.amount.replace(/[₹,]/g, '')), 0
            ).toLocaleString()} pending
          </p>
          <p className="text-xs text-gray-500 mt-1">Requires attention</p>
        </CardContent>
      </Card>
    </div>
  );
}
