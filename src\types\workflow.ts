
export interface WorkflowStatus {
  id: string;
  name: string;
  description: string;
  color: string;
  order: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface JobsheetServiceLink {
  id: string;
  jobsheet_id: string;
  service_id: string;
  linked_at: string;
  linked_by: string;
  relationship_type: 'created_from' | 'related_to' | 'spawned_from';
  notes?: string;
}

export interface ServiceTracking {
  id: string;
  service_id: string;
  status_changed_from?: string;
  status_changed_to: string;
  changed_at: string;
  changed_by: string;
  notes?: string;
  time_spent?: number; // in minutes
  parts_used?: string[];
  cost_breakdown?: {
    labor: number;
    parts: number;
    other: number;
  };
}

export interface WorkflowRule {
  id: string;
  name: string;
  trigger_condition: string;
  action_type: 'status_change' | 'notification' | 'assignment' | 'cost_update';
  action_config: Record<string, any>;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}
