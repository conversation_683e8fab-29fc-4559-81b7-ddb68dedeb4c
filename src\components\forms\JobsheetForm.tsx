
import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { CustomerSearch } from './CustomerSearch';
import { CustomerDialog } from '@/components/dialogs/CustomerDialog';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';
import type { Jobsheet, Customer, Service } from '@/types';

const jobsheetSchema = z.object({
  customer_name: z.string().min(1, 'Customer name is required'),
  phone: z.string().min(1, 'Phone number is required'),
  alternate_phone: z.string().optional(),
  email: z.string().email().optional().or(z.literal('')),
  address: z.string().optional(),
  device_type: z.enum(['Laptop', 'Desktop', 'Mobile', 'Tablet', 'Other']),
  device_brand: z.string().min(1, 'Device brand is required'),
  device_model: z.string().min(1, 'Device model is required'),
  serial_number: z.string().optional(),
  password: z.string().optional(),
  reported_issues: z.string().min(1, 'Reported issues are required'),
  physical_condition: z.string().min(1, 'Physical condition is required'),
  accessories_received: z.string().optional(),
  estimated_cost: z.string().optional(),
  advance_payment: z.string().optional(),
  expected_delivery: z.string().optional(),
  technician_assigned: z.string().optional(),
  priority: z.enum(['Low', 'Medium', 'High', 'Urgent']),
  status: z.enum(['Received', 'In Progress', 'Completed', 'Delivered', 'Cancelled']),
  warranty_terms: z.string().optional(),
  special_instructions: z.string().optional(),
  received_by: z.string().min(1, 'Received by is required'),
  customer_signature: z.boolean().optional(),
  terms_accepted: z.boolean().optional(),
  create_service_request: z.boolean().optional()
});

type JobsheetFormData = z.infer<typeof jobsheetSchema>;

interface JobsheetFormProps {
  jobsheet?: Jobsheet;
  customerData?: {
    customer_name: string;
    phone: string;
    email?: string;
    address?: string;
    alternate_phone?: string;
  };
  onSave: (data: Partial<Jobsheet>) => void;
  onCancel: () => void;
}

export function JobsheetForm({ jobsheet, customerData, onSave, onCancel }: JobsheetFormProps) {
  const { toast } = useToast();
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null);
  const [isCustomerDialogOpen, setIsCustomerDialogOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const form = useForm<JobsheetFormData>({
    resolver: zodResolver(jobsheetSchema),
    defaultValues: {
      customer_name: jobsheet?.customer_name || customerData?.customer_name || '',
      phone: jobsheet?.phone || customerData?.phone || '',
      alternate_phone: jobsheet?.alternate_phone || customerData?.alternate_phone || '',
      email: jobsheet?.email || customerData?.email || '',
      address: jobsheet?.address || customerData?.address || '',
      device_type: jobsheet?.device_type || 'Laptop',
      device_brand: jobsheet?.device_brand || '',
      device_model: jobsheet?.device_model || '',
      serial_number: jobsheet?.serial_number || '',
      password: jobsheet?.password || '',
      reported_issues: jobsheet?.reported_issues || '',
      physical_condition: jobsheet?.physical_condition || '',
      accessories_received: jobsheet?.accessories_received || '',
      estimated_cost: jobsheet?.estimated_cost || '',
      advance_payment: jobsheet?.advance_payment || '',
      expected_delivery: jobsheet?.expected_delivery || '',
      technician_assigned: jobsheet?.technician_assigned || '',
      priority: jobsheet?.priority || 'Medium',
      status: jobsheet?.status || 'Received',
      warranty_terms: jobsheet?.warranty_terms || '',
      special_instructions: jobsheet?.special_instructions || '',
      received_by: jobsheet?.received_by || 'Front Desk',
      customer_signature: jobsheet?.customer_signature || false,
      terms_accepted: jobsheet?.terms_accepted || false,
      create_service_request: false
    }
  });

  // Pre-populate form when customerData is provided
  useEffect(() => {
    if (customerData && !jobsheet) {
      form.setValue('customer_name', customerData.customer_name);
      form.setValue('phone', customerData.phone);
      form.setValue('alternate_phone', customerData.alternate_phone || '');
      form.setValue('email', customerData.email || '');
      form.setValue('address', customerData.address || '');
    }
  }, [customerData, jobsheet, form]);

  const handleCustomerSelect = (customer: Customer | null) => {
    setSelectedCustomer(customer);
    if (customer) {
      form.setValue('customer_name', customer.name);
      form.setValue('phone', customer.phone);
      form.setValue('alternate_phone', customer.alternatePhone || customer.alternate_phone || '');
      form.setValue('email', customer.email || '');
      form.setValue('address', customer.address || '');
    }
  };

  const handleNewCustomer = () => {
    setIsCustomerDialogOpen(true);
  };

  const handleSaveCustomer = async (customerData: any) => {
    try {
      const { data, error } = await supabase
        .from('customers')
        .insert([{
          ...customerData,
          user_id: 'mock-user-id' // Replace with actual user ID when auth is implemented
        }])
        .select()
        .single();

      if (error) throw error;

      if (data) {
        const newCustomer: Customer = {
          id: data.id,
          name: data.name,
          phone: data.phone,
          email: data.email,
          address: data.address,
          alternatePhone: data.alternate_phone,
          user_id: data.user_id
        };
        handleCustomerSelect(newCustomer);
        toast({
          title: "Customer Created",
          description: "New customer has been successfully created.",
        });
      }
    } catch (error) {
      console.error('Error creating customer:', error);
      toast({
        title: "Error",
        description: "Failed to create customer. Please try again.",
        variant: "destructive"
      });
    }
    setIsCustomerDialogOpen(false);
  };

  const onSubmit = async (data: JobsheetFormData) => {
    if (isSubmitting) return;
    setIsSubmitting(true);

    try {
      console.log('Form submitted with data:', data);
      
      // Ensure we have customer ID if customer is selected
      let customerId = selectedCustomer?.id;
      
      // If we don't have a customer ID but have customer data, try to find or create customer
      if (!customerId && (data.customer_name || data.phone)) {
        // Try to find existing customer by phone
        const { data: existingCustomer } = await supabase
          .from('customers')
          .select('id')
          .eq('phone', data.phone)
          .eq('user_id', 'mock-user-id')
          .single();

        if (existingCustomer) {
          customerId = existingCustomer.id;
        } else {
          // Create new customer
          const { data: newCustomer, error: customerError } = await supabase
            .from('customers')
            .insert([{
              name: data.customer_name,
              phone: data.phone,
              email: data.email || null,
              address: data.address || null,
              alternate_phone: data.alternate_phone || null,
              user_id: 'mock-user-id'
            }])
            .select('id')
            .single();

          if (customerError) throw customerError;
          customerId = newCustomer?.id;
        }
      }

      // Construct the jobsheet data properly
      const jobsheetData = {
        customer_id: customerId,
        customer_name: data.customer_name,
        phone: data.phone,
        alternate_phone: data.alternate_phone || null,
        email: data.email || null,
        address: data.address || null,
        device_type: data.device_type,
        device_brand: data.device_brand,
        device_model: data.device_model,
        serial_number: data.serial_number || null,
        password: data.password || null,
        reported_issues: data.reported_issues,
        physical_condition: data.physical_condition,
        accessories_received: data.accessories_received || null,
        estimated_cost: data.estimated_cost ? parseFloat(data.estimated_cost) : null,
        advance_payment: data.advance_payment ? parseFloat(data.advance_payment) : null,
        expected_delivery: data.expected_delivery || null,
        technician_assigned: data.technician_assigned || null,
        priority: data.priority,
        status: data.status,
        warranty_terms: data.warranty_terms || null,
        special_instructions: data.special_instructions || null,
        received_by: data.received_by,
        customer_signature: data.customer_signature || false,
        terms_accepted: data.terms_accepted || false,
        date: jobsheet?.date || new Date().toISOString().split('T')[0],
        user_id: 'mock-user-id' // Replace with actual user ID when auth is implemented
      };

      console.log('Saving jobsheet data:', jobsheetData);

      // Save the jobsheet to database
      let savedJobsheet;
      if (jobsheet) {
        // Update existing jobsheet
        const { data: updatedJobsheet, error } = await supabase
          .from('jobsheets')
          .update(jobsheetData)
          .eq('id', jobsheet.id)
          .select()
          .single();

        if (error) throw error;
        savedJobsheet = updatedJobsheet;
      } else {
        // Create new jobsheet
        const { data: newJobsheet, error } = await supabase
          .from('jobsheets')
          .insert(jobsheetData)
          .select()
          .single();

        if (error) throw error;
        savedJobsheet = newJobsheet;
      }

      // Create service request if requested and this is a new jobsheet
      if (data.create_service_request && !jobsheet && savedJobsheet) {
        const serviceData = {
          jobsheet_id: savedJobsheet.id,
          customer_id: customerId,
          customer: data.customer_name,
          phone: data.phone,
          device: `${data.device_brand} ${data.device_model}`,
          issue: data.reported_issues,
          status: 'Pending' as const,
          service_type: 'Repair' as const,
          priority: data.priority,
          technician: data.technician_assigned || null,
          cost: data.estimated_cost ? parseFloat(data.estimated_cost) : 0,
          user_id: 'mock-user-id'
        };

        const { error: serviceError } = await supabase
          .from('services')
          .insert([serviceData]);

        if (serviceError) throw serviceError;

        // Add comment about service creation
        await supabase
          .from('comments')
          .insert([{
            entity_type: 'jobsheet',
            entity_id: savedJobsheet.id,
            text: `Service request auto-created from jobsheet. Device: ${data.device_brand} ${data.device_model}. Physical condition: ${data.physical_condition}`,
            author: 'System',
            comment_type: 'edit'
          }]);

        toast({
          title: "Service Request Created",
          description: "A service request has been automatically created from this jobsheet.",
        });
      }

      // Call the original onSave callback for compatibility
      onSave(savedJobsheet);

      toast({
        title: jobsheet ? "Jobsheet Updated" : "Jobsheet Created",
        description: `Jobsheet has been successfully ${jobsheet ? 'updated' : 'created'}.`,
      });

    } catch (error) {
      console.error('Error saving jobsheet:', error);
      toast({
        title: "Error",
        description: "Failed to save jobsheet. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="space-y-6">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900">Customer Information</h3>
            
            {!customerData && (
              <CustomerSearch
                onCustomerSelect={handleCustomerSelect}
                onNewCustomer={handleNewCustomer}
                selectedCustomer={selectedCustomer}
              />
            )}

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="customer_name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Customer Name *</FormLabel>
                    <FormControl>
                      <Input {...field} disabled={!!customerData} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="phone"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Phone Number *</FormLabel>
                    <FormControl>
                      <Input {...field} disabled={!!customerData} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="alternate_phone"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Alternate Phone</FormLabel>
                    <FormControl>
                      <Input {...field} disabled={!!customerData} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Email</FormLabel>
                    <FormControl>
                      <Input {...field} type="email" disabled={!!customerData} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="address"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Address</FormLabel>
                  <FormControl>
                    <Textarea {...field} rows={2} disabled={!!customerData} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900">Device Information</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <FormField
                control={form.control}
                name="device_type"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Device Type *</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select device type" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="Laptop">Laptop</SelectItem>
                        <SelectItem value="Desktop">Desktop</SelectItem>
                        <SelectItem value="Mobile">Mobile</SelectItem>
                        <SelectItem value="Tablet">Tablet</SelectItem>
                        <SelectItem value="Other">Other</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="device_brand"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Brand *</FormLabel>
                    <FormControl>
                      <Input {...field} placeholder="e.g., Dell, HP, Apple" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="device_model"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Model *</FormLabel>
                    <FormControl>
                      <Input {...field} placeholder="e.g., Inspiron 15 3000" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="serial_number"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Serial Number</FormLabel>
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="password"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Device Password</FormLabel>
                    <FormControl>
                      <Input {...field} type="password" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </div>

          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900">Service Details</h3>

            <FormField
              control={form.control}
              name="reported_issues"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Reported Issues *</FormLabel>
                  <FormControl>
                    <Textarea {...field} rows={3} placeholder="Describe the issues reported by the customer" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="physical_condition"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Physical Condition *</FormLabel>
                  <FormControl>
                    <Textarea {...field} rows={2} placeholder="Describe the physical condition of the device" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="accessories_received"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Accessories Received</FormLabel>
                  <FormControl>
                    <Textarea {...field} rows={2} placeholder="List any accessories received with the device" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <FormField
                control={form.control}
                name="estimated_cost"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Estimated Cost</FormLabel>
                    <FormControl>
                      <Input {...field} placeholder="₹0" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="advance_payment"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Advance Payment</FormLabel>
                    <FormControl>
                      <Input {...field} placeholder="₹0" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="expected_delivery"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Expected Delivery</FormLabel>
                    <FormControl>
                      <Input {...field} type="date" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="technician_assigned"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Technician Assigned</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select technician" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="">Not assigned</SelectItem>
                        <SelectItem value="Mark Wilson">Mark Wilson</SelectItem>
                        <SelectItem value="Jane Smith">Jane Smith</SelectItem>
                        <SelectItem value="Bob Johnson">Bob Johnson</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="priority"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Priority *</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select priority" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="Low">Low</SelectItem>
                        <SelectItem value="Medium">Medium</SelectItem>
                        <SelectItem value="High">High</SelectItem>
                        <SelectItem value="Urgent">Urgent</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="status"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Status *</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select status" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="Received">Received</SelectItem>
                      <SelectItem value="In Progress">In Progress</SelectItem>
                      <SelectItem value="Completed">Completed</SelectItem>
                      <SelectItem value="Delivered">Delivered</SelectItem>
                      <SelectItem value="Cancelled">Cancelled</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900">Additional Information</h3>

            <FormField
              control={form.control}
              name="warranty_terms"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Warranty Terms</FormLabel>
                  <FormControl>
                    <Textarea {...field} rows={2} placeholder="Warranty terms and conditions" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="special_instructions"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Special Instructions</FormLabel>
                  <FormControl>
                    <Textarea {...field} rows={2} placeholder="Any special instructions or notes" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="received_by"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Received By *</FormLabel>
                  <FormControl>
                    <Input {...field} placeholder="Staff member name" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="space-y-3">
              <FormField
                control={form.control}
                name="customer_signature"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel>Customer Signature Obtained</FormLabel>
                    </div>
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="terms_accepted"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel>Terms and Conditions Accepted</FormLabel>
                    </div>
                  </FormItem>
                )}
              />

              {!jobsheet && (
                <FormField
                  control={form.control}
                  name="create_service_request"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                      <FormControl>
                        <Checkbox
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                      <div className="space-y-1 leading-none">
                        <FormLabel>Create Service Request Automatically</FormLabel>
                        <p className="text-sm text-gray-500">
                          This will create a service request in the Services module
                        </p>
                      </div>
                    </FormItem>
                  )}
                />
              )}
            </div>
          </div>

          <div className="flex justify-end space-x-2 pt-4">
            <Button type="button" variant="outline" onClick={onCancel}>
              Cancel
            </Button>
            <Button 
              type="submit" 
              className="bg-orange-500 hover:bg-orange-600"
              disabled={isSubmitting}
            >
              {isSubmitting ? 'Saving...' : (jobsheet ? 'Update' : 'Create')} Jobsheet
            </Button>
          </div>
        </form>
      </Form>

      <CustomerDialog
        isOpen={isCustomerDialogOpen}
        onClose={() => setIsCustomerDialogOpen(false)}
        customer={undefined}
        onSave={handleSaveCustomer}
      />
    </div>
  );
}
