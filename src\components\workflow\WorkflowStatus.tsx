
import { Badge } from "@/components/ui/badge";
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { CheckCircle, Clock, AlertCircle, FileText, Wrench, ClipboardList } from "lucide-react";
import type { Jobsheet, Service, Invoice } from "@/types";

interface WorkflowStatusProps {
  jobsheet?: Jobsheet;
  service?: Service;
  invoice?: Invoice;
  className?: string;
}

export function WorkflowStatus({ jobsheet, service, invoice, className }: WorkflowStatusProps) {
  const getStatusIcon = (status: string, isCompleted: boolean) => {
    if (isCompleted) return <CheckCircle className="w-4 h-4 text-green-500" />;
    if (status === 'In Progress') return <Clock className="w-4 h-4 text-orange-500" />;
    return <AlertCircle className="w-4 h-4 text-gray-400" />;
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Completed':
      case 'Delivered':
      case 'Paid':
        return 'bg-green-100 text-green-800';
      case 'In Progress':
      case 'Pending':
        return 'bg-orange-100 text-orange-800';
      case 'Cancelled':
      case 'Overdue':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <Card className={className}>
      <CardHeader className="pb-3">
        <CardTitle className="text-sm font-medium">Workflow Progress</CardTitle>
      </CardHeader>
      <CardContent className="space-y-3">
        {/* Jobsheet Status */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <ClipboardList className="w-4 h-4 text-blue-500" />
            <span className="text-sm font-medium">Jobsheet</span>
          </div>
          <div className="flex items-center gap-2">
            {jobsheet && (
              <>
                {getStatusIcon(jobsheet.status, jobsheet.status === 'Completed' || jobsheet.status === 'Delivered')}
                <Badge variant="secondary" className={getStatusColor(jobsheet.status)}>
                  {jobsheet.status}
                </Badge>
              </>
            )}
            {!jobsheet && (
              <>
                <AlertCircle className="w-4 h-4 text-gray-400" />
                <Badge variant="secondary" className="bg-gray-100 text-gray-800">
                  Not Created
                </Badge>
              </>
            )}
          </div>
        </div>

        {/* Service Status */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Wrench className="w-4 h-4 text-green-500" />
            <span className="text-sm font-medium">Service</span>
          </div>
          <div className="flex items-center gap-2">
            {service && (
              <>
                {getStatusIcon(service.status, service.status === 'Completed')}
                <Badge variant="secondary" className={getStatusColor(service.status)}>
                  {service.status}
                </Badge>
              </>
            )}
            {!service && (
              <>
                <AlertCircle className="w-4 h-4 text-gray-400" />
                <Badge variant="secondary" className="bg-gray-100 text-gray-800">
                  Not Created
                </Badge>
              </>
            )}
          </div>
        </div>

        {/* Invoice Status */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <FileText className="w-4 h-4 text-purple-500" />
            <span className="text-sm font-medium">Invoice</span>
          </div>
          <div className="flex items-center gap-2">
            {invoice && (
              <>
                {getStatusIcon(invoice.status || 'Draft', invoice.status === 'Paid')}
                <Badge variant="secondary" className={getStatusColor(invoice.status || 'Draft')}>
                  {invoice.status || 'Draft'}
                </Badge>
              </>
            )}
            {!invoice && (
              <>
                <AlertCircle className="w-4 h-4 text-gray-400" />
                <Badge variant="secondary" className="bg-gray-100 text-gray-800">
                  Not Created
                </Badge>
              </>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
