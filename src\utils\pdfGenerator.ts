
import type { Invoice, Jobsheet, Service } from '@/types';

// This is a basic PDF generation utility
// For production, you might want to use libraries like jsPDF, PDFKit, or react-pdf
export class PDFGenerator {
  static generateJobsheetPDF(jobsheet: Jobsheet): void {
    const content = `
      JOBSHEET - ${jobsheet.id}
      
      Customer Information:
      Name: ${jobsheet.customer_name}
      Phone: ${jobsheet.phone}
      Email: ${jobsheet.email || 'N/A'}
      Address: ${jobsheet.address || 'N/A'}
      
      Device Information:
      Type: ${jobsheet.device_type}
      Brand: ${jobsheet.device_brand}
      Model: ${jobsheet.device_model}
      Serial Number: ${jobsheet.serial_number || 'N/A'}
      
      Service Details:
      Issues Reported: ${jobsheet.reported_issues}
      Physical Condition: ${jobsheet.physical_condition}
      Accessories: ${jobsheet.accessories_received || 'None'}
      
      Service Information:
      Priority: ${jobsheet.priority}
      Status: ${jobsheet.status}
      Technician: ${jobsheet.technician_assigned || 'Not assigned'}
      Expected Delivery: ${jobsheet.expected_delivery || 'TBD'}
      Estimated Cost: ₹${jobsheet.estimated_cost || '0'}
      
      Terms & Conditions:
      ${jobsheet.warranty_terms || 'Standard warranty terms apply'}
      
      Special Instructions:
      ${jobsheet.special_instructions || 'None'}
      
      Date: ${jobsheet.date}
      Received By: ${jobsheet.received_by}
    `;

    this.downloadTextAsPDF(content, `jobsheet-${jobsheet.id}.pdf`);
  }

  static generateInvoicePDF(invoice: Invoice): void {
    const content = `
      INVOICE - ${invoice.id}
      
      Bill To:
      ${invoice.customer}
      Phone: ${invoice.phone}
      Email: ${invoice.email || 'N/A'}
      Address: ${invoice.address || 'N/A'}
      
      Service Details:
      Device: ${invoice.device}
      Issue: ${invoice.issue}
      
      Amount Details:
      Subtotal: ₹${invoice.amount}
      GST: ₹${invoice.gst || '0'}
      Total: ₹${invoice.amount}
      
      Status: ${invoice.status}
      Date: ${invoice.date}
      Due Date: ${invoice.due_date || 'Immediate'}
      
      Payment Terms: ${invoice.payment_terms || 'Cash on delivery'}
      
      Notes:
      ${invoice.notes || 'Thank you for your business!'}
    `;

    this.downloadTextAsPDF(content, `invoice-${invoice.id}.pdf`);
  }

  static generateServiceReportPDF(service: Service): void {
    const content = `
      SERVICE REPORT - ${service.id}
      
      Customer: ${service.customer}
      Phone: ${service.phone}
      Device: ${service.device}
      
      Service Details:
      Issue: ${service.issue}
      Status: ${service.status}
      Type: ${service.service_type}
      Priority: ${service.priority}
      
      Technician: ${service.technician || 'Not assigned'}
      
      Timeline:
      Estimated Completion: ${service.estimated_completion || 'TBD'}
      Actual Completion: ${service.actual_completion || 'In progress'}
      
      Cost Breakdown:
      Parts Cost: ₹${service.parts_cost || '0'}
      Labor Cost: ₹${service.labor_cost || '0'}
      Total Cost: ₹${service.cost || '0'}
      
      Warranty: ${service.warranty_period || 0} days
    `;

    this.downloadTextAsPDF(content, `service-report-${service.id}.pdf`);
  }

  private static downloadTextAsPDF(content: string, filename: string): void {
    // For now, we'll create a simple text file
    // In production, you'd use a proper PDF library
    const blob = new Blob([content], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }
}
