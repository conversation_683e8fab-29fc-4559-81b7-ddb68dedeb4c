
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Eye, Calendar, DollarSign, TrendingUp } from "lucide-react";
import { useAppData } from "@/contexts/AppDataContext";
import { EnhancedReportsView } from "@/components/analytics/EnhancedReportsView";

export default function Dashboard() {
  const { customers, services, invoices, expenses, loading } = useAppData();

  // Add console logs for debugging
  console.log('Dashboard data:', { customers, services, invoices, expenses, loading });

  // Helper function to safely convert amount to number
  const getNumericAmount = (amount: string | number): number => {
    if (typeof amount === 'number') return amount;
    return parseFloat(amount.replace(/[₹,]/g, '') || '0');
  };

  const totalRevenue = invoices
    .filter(inv => inv.status === 'Paid')
    .reduce((sum, inv) => sum + getNumericAmount(inv.amount), 0);

  const totalExpenses = expenses
    .reduce((sum, exp) => sum + getNumericAmount(exp.amount), 0);

  const activeServices = services.filter(s => s.status === 'In Progress').length;

  const stats = [
    {
      title: "Total Revenue",
      value: `₹${totalRevenue.toLocaleString()}`,
      icon: DollarSign,
      change: "+12%",
      description: "from last month"
    },
    {
      title: "Active Services",
      value: activeServices.toString(),
      icon: Eye,
      change: "+5%",
      description: "from last week"
    },
    {
      title: "Total Customers",
      value: customers.length.toString(),
      icon: Calendar,
      change: "+8%",
      description: "new customers"
    },
    {
      title: "Net Profit",
      value: `₹${(totalRevenue - totalExpenses).toLocaleString()}`,
      icon: TrendingUp,
      change: "+15%",
      description: "this month"
    }
  ];

  // Prepare analytics data for EnhancedReportsView
  const analyticsData = {
    totalRevenue,
    totalExpenses,
    profitMargin: totalRevenue > 0 ? ((totalRevenue - totalExpenses) / totalRevenue) * 100 : 0,
    customerGrowth: 8.2,
    serviceCompletion: 92.5,
    inventoryTurnover: 4.2,
    monthlyData: [
      { month: 'Jan', revenue: 120000, expenses: 45000, profit: 75000 },
      { month: 'Feb', revenue: 135000, expenses: 48000, profit: 87000 },
      { month: 'Mar', revenue: 128000, expenses: 52000, profit: 76000 },
      { month: 'Apr', revenue: 145000, expenses: 50000, profit: 95000 },
      { month: 'May', revenue: 158000, expenses: 55000, profit: 103000 },
      { month: 'Jun', revenue: 172000, expenses: 58000, profit: 114000 },
    ],
    customerData: customers.slice(0, 5).map(customer => ({
      name: customer.name,
      revenue: getNumericAmount(customer.totalSpent || '0')
    })),
    serviceData: [
      { name: 'Pending', value: services.filter(s => s.status === 'Pending').length },
      { name: 'In Progress', value: services.filter(s => s.status === 'In Progress').length },
      { name: 'Completed', value: services.filter(s => s.status === 'Completed').length },
    ],
    inventoryData: [
      { category: 'Screens', stock: 15, reorderLevel: 5 },
      { category: 'Batteries', stock: 8, reorderLevel: 10 },
      { category: 'Cables', stock: 25, reorderLevel: 8 },
    ],
    expenseData: [
      { category: 'Office', amount: 25000 },
      { category: 'Supplies', amount: 18000 },
      { category: 'Marketing', amount: 12000 },
    ]
  };

  // Show loading state
  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-orange-500 mx-auto"></div>
          <p className="mt-2 text-gray-600">Loading dashboard...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-gray-900">Dashboard</h1>
        <p className="text-gray-600">Welcome back! Here's your business overview.</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat, index) => (
          <Card key={index}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">{stat.title}</CardTitle>
              <stat.icon className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stat.value}</div>
              <p className="text-xs text-muted-foreground">
                <span className="text-green-600">{stat.change}</span> {stat.description}
              </p>
            </CardContent>
          </Card>
        ))}
      </div>

      <EnhancedReportsView data={analyticsData} />
    </div>
  );
}
