
import { useState, useCallback } from 'react';
import { useAppData } from '@/contexts/AppDataContext';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';
import type { Jobsheet, Service, Invoice, Comment } from '@/types';

export function useWorkflowManager() {
  const { addService, addInvoice, services } = useAppData();
  const [isProcessing, setIsProcessing] = useState(false);

  // Generate a proper UUID for mock user ID (same as used in AppDataContext)
  const mockUserId = 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11';

  // Create service from jobsheet
  const createServiceFromJobsheet = useCallback(async (jobsheet: Jobsheet) => {
    if (isProcessing) return null;
    setIsProcessing(true);

    try {
      // Check if service already exists
      const existingService = services.find(s => s.jobsheet_id === jobsheet.id);
      
      if (existingService) {
        toast.info('Service already exists for this jobsheet');
        return existingService;
      }

      const serviceData = {
        customer: jobsheet.customer_name,
        phone: jobsheet.phone,
        device: `${jobsheet.device_type} - ${jobsheet.device_brand} ${jobsheet.device_model}`,
        issue: jobsheet.reported_issues,
        status: 'In Progress' as const,
        service_type: 'Repair' as const,
        priority: jobsheet.priority,
        technician: jobsheet.technician_assigned || 'Not assigned',
        estimated_completion: jobsheet.expected_delivery,
        cost: jobsheet.estimated_cost ? parseFloat(jobsheet.estimated_cost) : 0,
        parts_cost: 0,
        labor_cost: jobsheet.estimated_cost ? parseFloat(jobsheet.estimated_cost) : 0,
        customer_id: jobsheet.customer_id,
        jobsheet_id: jobsheet.id,
        warranty_period: 90,
        user_id: mockUserId,
        comments: [{
          id: `comment-${Date.now()}`,
          text: `Service created from Jobsheet #${jobsheet.id}`,
          author: 'System',
          type: 'system' as const,
          created_at: new Date().toISOString(),
          timestamp: new Date().toISOString()
        }]
      };

      await addService(serviceData);
      toast.success('Service created successfully from jobsheet');
      return serviceData;
    } catch (error) {
      console.error('Error creating service from jobsheet:', error);
      toast.error('Failed to create service from jobsheet');
      return null;
    } finally {
      setIsProcessing(false);
    }
  }, [addService, services, isProcessing, mockUserId]);

  // Create invoice from completed service
  const createInvoiceFromService = useCallback(async (service: Service) => {
    if (isProcessing) return null;
    setIsProcessing(true);

    try {
      const invoiceData = {
        customer: service.customer,
        phone: service.phone,
        device: service.device,
        issue: service.issue,
        amount: service.cost?.toString() || '0',
        date: new Date().toISOString().split('T')[0],
        status: 'Draft' as const,
        customer_id: service.customer_id,
        service_id: service.id,
        estimated_amount: service.cost?.toString(),
        inspection_fee: '500',
        gst: '0',
        tax_amount: 0,
        discount_amount: 0,
        user_id: mockUserId,
        usedParts: [],
        expenses: [],
        comments: [{
          id: `comment-${Date.now()}`,
          text: `Invoice created from completed service #${service.id}`,
          author: 'System',
          type: 'system' as const,
          created_at: new Date().toISOString(),
          timestamp: new Date().toISOString()
        }]
      };

      await addInvoice(invoiceData);
      toast.success('Invoice created successfully from service');
      return invoiceData;
    } catch (error) {
      console.error('Error creating invoice from service:', error);
      toast.error('Failed to create invoice from service');
      return null;
    } finally {
      setIsProcessing(false);
    }
  }, [addInvoice, isProcessing, mockUserId]);

  // Add comment to entity
  const addCommentToEntity = useCallback(async (
    entityId: string,
    entityType: 'jobsheet' | 'service' | 'invoice' | 'customer',
    text: string,
    author: string = 'Current User',
    commentType: 'edit' | 'customer' | 'internal' | 'status_change' | 'system' = 'internal'
  ) => {
    try {
      const { error } = await supabase
        .from('comments')
        .insert({
          entity_id: entityId,
          entity_type: entityType,
          text,
          author,
          comment_type: commentType
        });

      if (error) {
        console.error('Error adding comment:', error);
        return false;
      }
      return true;
    } catch (error) {
      console.error('Error adding comment:', error);
      return false;
    }
  }, []);

  // Get comments for entity
  const getCommentsForEntity = useCallback(async (
    entityId: string,
    entityType: 'jobsheet' | 'service' | 'invoice' | 'customer'
  ): Promise<Comment[]> => {
    try {
      const { data, error } = await supabase
        .from('comments')
        .select('*')
        .eq('entity_id', entityId)
        .eq('entity_type', entityType)
        .order('created_at', { ascending: true });

      if (error) {
        console.error('Error fetching comments:', error);
        return [];
      }

      return data?.map(comment => ({
        id: comment.id,
        text: comment.text,
        author: comment.author,
        type: comment.comment_type as Comment['type'],
        created_at: comment.created_at,
        timestamp: comment.timestamp || comment.created_at
      })) || [];
    } catch (error) {
      console.error('Error fetching comments:', error);
      return [];
    }
  }, []);

  return {
    createServiceFromJobsheet,
    createInvoiceFromService,
    addCommentToEntity,
    getCommentsForEntity,
    isProcessing
  };
}
