
import { Dialog, DialogContent } from "@/components/ui/dialog";
import { useFrontendData } from "@/contexts/FrontendDataContext";
import { InvoiceForm } from "./invoice/InvoiceForm";
import { PartsManager } from "./invoice/PartsManager";
import { ExpensesManager } from "./invoice/ExpensesManager";
import { CommentsSection } from "./invoice/CommentsSection";
import { InvoiceDialogHeader } from "./invoice/InvoiceDialogHeader";
import { InvoiceFormActions } from "./invoice/InvoiceFormActions";
import { useInvoiceFormLogic } from "./invoice/InvoiceFormLogic";
import { useInvoiceSubmitLogic } from "./invoice/InvoiceSubmitLogic";
import { useInvoiceValidation } from "@/hooks/useInvoiceValidation";
import { useWorkflowAutomation } from "@/hooks/useWorkflowAutomation";
import { NotificationCenter } from "@/components/workflow/NotificationCenter";
import type { Invoice } from "../../types";

interface InvoiceDialogProps {
  isOpen: boolean;
  onClose: () => void;
  invoice?: Invoice;
  onSave: (invoice: Invoice) => void;
}

export function InvoiceDialog({ isOpen, onClose, invoice, onSave }: InvoiceDialogProps) {
  const { addExpense, services } = useFrontendData();
  const { createSubmitHandler } = useInvoiceSubmitLogic();
  const { sendStatusNotification } = useWorkflowAutomation();
  
  const {
    validationErrors,
    validateInvoice,
    validateExpenses,
    validateParts,
    clearValidationErrors,
    getFieldError
  } = useInvoiceValidation();
  
  const {
    formData,
    setFormData,
    originalData,
    newComment,
    setNewComment,
    commentType,
    setCommentType,
    handleUpdateParts,
    handleUpdateExpenses
  } = useInvoiceFormLogic(invoice, isOpen);

  const updateInventory = async (updates: any) => {
    console.log('Inventory update requested:', updates);
  };

  const associatedService = invoice?.id ? services.find(s => s.id === invoice.id) : null;

  const getAllComments = () => {
    const invoiceComments = formData.comments || [];
    const serviceComments = associatedService?.comments || [];
    
    const allComments = [...invoiceComments, ...serviceComments].sort(
      (a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
    );
    
    return allComments;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    clearValidationErrors();

    // Validate main invoice data
    const isInvoiceValid = validateInvoice(formData);
    
    // Validate expenses
    const areExpensesValid = validateExpenses(formData.expenses || []);
    
    // Validate parts
    const arePartsValid = validateParts(formData.usedParts || []);

    if (isInvoiceValid && areExpensesValid && arePartsValid) {
      // Send workflow notifications if status changed
      if (originalData && originalData.status !== formData.status) {
        sendStatusNotification('invoice', formData, originalData.status || 'Draft', formData.status || 'Pending');
      }

      const submitHandler = createSubmitHandler(
        formData,
        originalData,
        newComment,
        commentType,
        addExpense,
        onSave,
        onClose,
        invoice
      );
      submitHandler(e);
    }
  };

  // Create validation errors object for easy access
  const fieldErrors = validationErrors.reduce((acc, error) => {
    acc[error.field] = error.message;
    return acc;
  }, {} as { [key: string]: string });

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[1200px] max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between mb-4">
          <InvoiceDialogHeader invoice={invoice} />
          <NotificationCenter 
            services={services}
            invoices={[formData]}
            inventory={[]}
          />
        </div>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div className="lg:col-span-2 space-y-4">
              <InvoiceForm 
                formData={formData} 
                setFormData={setFormData}
                validationErrors={fieldErrors}
              />
              
              <PartsManager
                usedParts={formData.usedParts || []}
                onUpdateParts={handleUpdateParts}
                validationErrors={fieldErrors}
              />

              <ExpensesManager
                expenses={formData.expenses || []}
                onUpdateExpenses={handleUpdateExpenses}
                validationErrors={fieldErrors}
              />
            </div>

            <div className="space-y-4">
              <CommentsSection
                allComments={getAllComments()}
                newComment={newComment}
                setNewComment={setNewComment}
                commentType={commentType}
                setCommentType={setCommentType}
              />
            </div>
          </div>

          <InvoiceFormActions invoice={invoice} onClose={onClose} />
        </form>
      </DialogContent>
    </Dialog>
  );
}
