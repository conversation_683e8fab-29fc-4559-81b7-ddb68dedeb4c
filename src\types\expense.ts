
export interface Expense {
  id: string;
  date: string;
  vendor: string;
  description: string;
  amount: string;
  category: 'Office' | 'Marketing' | 'Equipment' | 'Utilities' | 'Travel' | 'Parts' | 'Labor' | 'Shipping' | 'Other';
  receipt?: 'Available' | 'Pending' | 'Missing';
  invoice_id?: string;
  tax_amount?: number;
  payment_method?: string;
  reference_number?: string;
  approved_by?: string;
  department?: string;
  user_id: string;
}

export interface ExpenseItem {
  id: string;
  description: string;
  amount: number;
  category: 'Office' | 'Marketing' | 'Equipment' | 'Utilities' | 'Travel' | 'Parts' | 'Labor' | 'Shipping' | 'Other';
  invoice_id: string;
}
