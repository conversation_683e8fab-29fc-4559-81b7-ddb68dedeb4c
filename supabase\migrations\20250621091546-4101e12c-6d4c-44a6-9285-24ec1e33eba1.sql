
-- Add missing columns to invoices table for enhanced billing
ALTER TABLE public.invoices 
ADD COLUMN IF NOT EXISTS tax_amount NUMERIC DEFAULT 0,
ADD COLUMN IF NOT EXISTS discount_amount NUMERIC DEFAULT 0,
ADD COLUMN IF NOT EXISTS due_date DATE,
ADD COLUMN IF NOT EXISTS payment_method TEXT,
ADD COLUMN IF NOT EXISTS payment_terms TEXT DEFAULT '30 days',
ADD COLUMN IF NOT EXISTS notes TEXT;

-- Add missing columns to services table for enhanced tracking
ALTER TABLE public.services 
ADD COLUMN IF NOT EXISTS priority TEXT DEFAULT 'Medium',
ADD COLUMN IF NOT EXISTS estimated_completion DATE,
ADD COLUMN IF NOT EXISTS actual_completion DATE,
ADD COLUMN IF NOT EXISTS warranty_period INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS service_type TEXT DEFAULT 'Repair',
ADD COLUMN IF NOT EXISTS parts_cost NUMERIC DEFAULT 0,
ADD COLUMN IF NOT EXISTS labor_cost NUMERIC DEFAULT 0;

-- Add missing columns to customers table for better management
ALTER TABLE public.customers 
ADD COLUMN IF NOT EXISTS customer_type TEXT DEFAULT 'Individual',
ADD COLUMN IF NOT EXISTS credit_limit NUMERIC DEFAULT 0,
ADD COLUMN IF NOT EXISTS payment_terms TEXT DEFAULT '30 days',
ADD COLUMN IF NOT EXISTS tax_id TEXT,
ADD COLUMN IF NOT EXISTS preferred_contact TEXT DEFAULT 'Phone',
ADD COLUMN IF NOT EXISTS notes TEXT,
ADD COLUMN IF NOT EXISTS last_service_date DATE;

-- Add missing columns to inventory_items for better tracking
ALTER TABLE public.inventory_items 
ADD COLUMN IF NOT EXISTS sku TEXT,
ADD COLUMN IF NOT EXISTS barcode TEXT,
ADD COLUMN IF NOT EXISTS reorder_point INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS max_stock INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS cost_price NUMERIC DEFAULT 0,
ADD COLUMN IF NOT EXISTS markup_percentage NUMERIC DEFAULT 0,
ADD COLUMN IF NOT EXISTS warranty_months INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS bin_location TEXT,
ADD COLUMN IF NOT EXISTS last_restocked DATE;

-- Add missing columns to expenses for enhanced tracking
ALTER TABLE public.expenses 
ADD COLUMN IF NOT EXISTS tax_amount NUMERIC DEFAULT 0,
ADD COLUMN IF NOT EXISTS payment_method TEXT DEFAULT 'Cash',
ADD COLUMN IF NOT EXISTS reference_number TEXT,
ADD COLUMN IF NOT EXISTS approved_by TEXT,
ADD COLUMN IF NOT EXISTS department TEXT DEFAULT 'General';

-- Create service_parts junction table for tracking parts used in services
CREATE TABLE IF NOT EXISTS public.service_parts (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  service_id UUID NOT NULL REFERENCES public.services(id) ON DELETE CASCADE,
  item_id UUID REFERENCES public.inventory_items(id) ON DELETE SET NULL,
  item_name TEXT NOT NULL,
  quantity INTEGER NOT NULL DEFAULT 1,
  unit_price NUMERIC NOT NULL DEFAULT 0,
  total_price NUMERIC NOT NULL DEFAULT 0,
  is_warranty BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create service_timeline table for tracking service history
CREATE TABLE IF NOT EXISTS public.service_timeline (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  service_id UUID NOT NULL REFERENCES public.services(id) ON DELETE CASCADE,
  status_from TEXT,
  status_to TEXT NOT NULL,
  changed_by TEXT NOT NULL,
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create payment_records table for tracking payments
CREATE TABLE IF NOT EXISTS public.payment_records (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  invoice_id UUID NOT NULL REFERENCES public.invoices(id) ON DELETE CASCADE,
  amount NUMERIC NOT NULL,
  payment_method TEXT NOT NULL,
  payment_date DATE NOT NULL DEFAULT CURRENT_DATE,
  reference_number TEXT,
  notes TEXT,
  created_by TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS on new tables
ALTER TABLE public.service_parts ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.service_timeline ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.payment_records ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for service_parts
CREATE POLICY "Users can manage service parts for their services" ON public.service_parts
  FOR ALL USING (EXISTS (
    SELECT 1 FROM public.services 
    WHERE services.id = service_parts.service_id 
    AND services.user_id = auth.uid()
  ));

-- Create RLS policies for service_timeline
CREATE POLICY "Users can manage service timeline for their services" ON public.service_timeline
  FOR ALL USING (EXISTS (
    SELECT 1 FROM public.services 
    WHERE services.id = service_timeline.service_id 
    AND services.user_id = auth.uid()
  ));

-- Create RLS policies for payment_records
CREATE POLICY "Users can manage payment records for their invoices" ON public.payment_records
  FOR ALL USING (EXISTS (
    SELECT 1 FROM public.invoices 
    WHERE invoices.id = payment_records.invoice_id 
    AND invoices.user_id = auth.uid()
  ));

-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS idx_service_parts_service_id ON public.service_parts(service_id);
CREATE INDEX IF NOT EXISTS idx_service_timeline_service_id ON public.service_timeline(service_id);
CREATE INDEX IF NOT EXISTS idx_payment_records_invoice_id ON public.payment_records(invoice_id);
CREATE INDEX IF NOT EXISTS idx_customers_last_service_date ON public.customers(last_service_date);
CREATE INDEX IF NOT EXISTS idx_services_estimated_completion ON public.services(estimated_completion);
CREATE INDEX IF NOT EXISTS idx_inventory_reorder_point ON public.inventory_items(reorder_point);

-- Create triggers for automatic timeline tracking
CREATE OR REPLACE FUNCTION public.track_service_status_changes()
RETURNS TRIGGER
LANGUAGE plpgsql
AS $$
BEGIN
  IF OLD.status IS DISTINCT FROM NEW.status THEN
    INSERT INTO public.service_timeline (service_id, status_from, status_to, changed_by, notes)
    VALUES (NEW.id, OLD.status, NEW.status, 'System', 'Status automatically updated');
  END IF;
  RETURN NEW;
END;
$$;

CREATE TRIGGER service_status_change_trigger
  AFTER UPDATE ON public.services
  FOR EACH ROW
  EXECUTE FUNCTION public.track_service_status_changes();

-- Create function to update customer last service date
CREATE OR REPLACE FUNCTION public.update_customer_last_service()
RETURNS TRIGGER
LANGUAGE plpgsql
AS $$
BEGIN
  IF NEW.status = 'Completed' AND OLD.status != 'Completed' THEN
    UPDATE public.customers 
    SET last_service_date = CURRENT_DATE
    WHERE id = NEW.customer_id;
  END IF;
  RETURN NEW;
END;
$$;

CREATE TRIGGER update_customer_service_date_trigger
  AFTER UPDATE ON public.services
  FOR EACH ROW
  EXECUTE FUNCTION public.update_customer_last_service();
