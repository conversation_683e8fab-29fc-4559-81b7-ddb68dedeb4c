
import React from "react";
import { Button } from "@/components/ui/button";
import { CustomerBasicInfo } from "./customer/CustomerBasicInfo";
import { CustomerAddressSection } from "./customer/CustomerAddressSection";
import { CustomerBusinessInfo } from "./customer/CustomerBusinessInfo";
import type { Customer } from "../../types";

interface EnhancedCustomerFormProps {
  formData: Partial<Customer>;
  setFormData: (data: Partial<Customer>) => void;
  onSubmit: (e: React.FormEvent) => void;
  onCancel: () => void;
  isLoading?: boolean;
}

export function EnhancedCustomerForm({ 
  formData, 
  setFormData, 
  onSubmit, 
  onCancel,
  isLoading = false 
}: EnhancedCustomerFormProps) {
  const handleInputChange = (field: keyof Customer, value: string | number) => {
    setFormData({ ...formData, [field]: value });
  };

  return (
    <form onSubmit={onSubmit} className="space-y-6">
      <CustomerBasicInfo formData={formData} handleInputChange={handleInputChange} />
      <CustomerAddressSection formData={formData} handleInputChange={handleInputChange} />
      <CustomerBusinessInfo formData={formData} handleInputChange={handleInputChange} />

      <div className="flex justify-end space-x-4">
        <Button type="button" variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button type="submit" disabled={isLoading}>
          {isLoading ? "Saving..." : "Save Customer"}
        </Button>
      </div>
    </form>
  );
}
