
import React from "react";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import type { Service } from "../../../types";

interface ServiceCostBreakdownProps {
  formData: Partial<Service>;
  handleInputChange: (field: keyof Service, value: string | number) => void;
}

export function ServiceCostBreakdown({ formData, handleInputChange }: ServiceCostBreakdownProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg">Cost Breakdown</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium mb-1">Parts Cost (₹)</label>
            <Input
              type="number"
              value={formData.parts_cost || ""}
              onChange={(e) => handleInputChange("parts_cost", parseFloat(e.target.value) || 0)}
            />
          </div>
          <div>
            <label className="block text-sm font-medium mb-1">Labor Cost (₹)</label>
            <Input
              type="number"
              value={formData.labor_cost || ""}
              onChange={(e) => handleInputChange("labor_cost", parseFloat(e.target.value) || 0)}
            />
          </div>
          <div>
            <label className="block text-sm font-medium mb-1">Total Cost (₹)</label>
            <Input
              type="number"
              value={formData.cost || ""}
              onChange={(e) => handleInputChange("cost", parseFloat(e.target.value) || 0)}
            />
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
