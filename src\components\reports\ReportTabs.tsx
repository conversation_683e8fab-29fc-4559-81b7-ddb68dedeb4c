
import { But<PERSON> } from "@/components/ui/button";
import { Wrench, Users, TrendingUp, Receipt } from "lucide-react";

interface ReportTabsProps {
  activeTab: string;
  setActiveTab: (tab: string) => void;
}

const tabs = [
  { name: "Advanced", icon: TrendingUp },
  { name: "Service", icon: Wrench },
  { name: "Customer", icon: Users },
  { name: "Profit & Loss", icon: TrendingUp },
  { name: "Expenses", icon: Receipt }
];

export function ReportTabs({ activeTab, setActiveTab }: ReportTabsProps) {
  return (
    <div className="flex space-x-2 border-b border-gray-200">
      {tabs.map(({ name, icon: Icon }) => (
        <Button
          key={name}
          variant={activeTab === name ? "default" : "ghost"}
          onClick={() => setActiveTab(name)}
          className={activeTab === name ? "bg-orange-500 hover:bg-orange-600" : ""}
        >
          <Icon className="w-4 h-4 mr-2" />
          {name}
        </Button>
      ))}
    </div>
  );
}
