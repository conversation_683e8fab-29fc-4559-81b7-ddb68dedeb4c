
import React from "react";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import type { Customer } from "../../../types";

interface CustomerBusinessInfoProps {
  formData: Partial<Customer>;
  handleInputChange: (field: keyof Customer, value: string | number) => void;
}

export function CustomerBusinessInfo({ formData, handleInputChange }: CustomerBusinessInfoProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg">Business Information</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium mb-1">Tax ID</label>
            <Input
              value={formData.tax_id || ""}
              onChange={(e) => handleInputChange("tax_id", e.target.value)}
            />
          </div>
          <div>
            <label className="block text-sm font-medium mb-1">Credit Limit (₹)</label>
            <Input
              type="number"
              value={formData.credit_limit || ""}
              onChange={(e) => handleInputChange("credit_limit", parseFloat(e.target.value) || 0)}
            />
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium mb-1">Payment Terms</label>
            <Select
              value={formData.payment_terms || "30 days"}
              onValueChange={(value) => handleInputChange("payment_terms", value)}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="Cash">Cash</SelectItem>
                <SelectItem value="15 days">15 days</SelectItem>
                <SelectItem value="30 days">30 days</SelectItem>
                <SelectItem value="45 days">45 days</SelectItem>
                <SelectItem value="60 days">60 days</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div>
            <label className="block text-sm font-medium mb-1">Warranty</label>
            <Input
              value={formData.warranty || ""}
              onChange={(e) => handleInputChange("warranty", e.target.value)}
              placeholder="e.g., 1 year, 6 months"
            />
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium mb-1">Notes</label>
          <Textarea
            value={formData.notes || ""}
            onChange={(e) => handleInputChange("notes", e.target.value)}
            placeholder="Additional notes about the customer"
          />
        </div>
      </CardContent>
    </Card>
  );
}
