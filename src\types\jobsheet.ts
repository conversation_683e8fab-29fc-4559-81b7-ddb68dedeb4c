
import { Comment } from './common';

export interface Jobsheet {
  id: string;
  date: string;
  customer_id?: string;
  customer_name: string;
  phone: string;
  alternate_phone?: string;
  email?: string;
  address?: string;
  device_type: 'Laptop' | 'Desktop' | 'Mobile' | 'Tablet' | 'Other';
  device_brand: string;
  device_model: string;
  serial_number?: string;
  password?: string;
  reported_issues: string;
  physical_condition: string;
  accessories_received: string;
  estimated_cost?: string;
  advance_payment?: string;
  expected_delivery?: string;
  technician_assigned?: string;
  priority: 'Low' | 'Medium' | 'High' | 'Urgent';
  status: 'Received' | 'In Progress' | 'Completed' | 'Delivered' | 'Cancelled';
  warranty_terms?: string;
  special_instructions?: string;
  received_by: string;
  customer_signature?: boolean;
  terms_accepted?: boolean;
  user_id: string;
  comments?: Comment[];
  created_at?: string;
  updated_at?: string;
}
