
import React from "react";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import type { Customer } from "../../../types";

interface CustomerAddressSectionProps {
  formData: Partial<Customer>;
  handleInputChange: (field: keyof Customer, value: string | number) => void;
}

export function CustomerAddressSection({ formData, handleInputChange }: CustomerAddressSectionProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg">Address Information</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <label className="block text-sm font-medium mb-1">Address *</label>
          <Textarea
            value={formData.address || ""}
            onChange={(e) => handleInputChange("address", e.target.value)}
            required
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium mb-1">City</label>
            <Input
              value={formData.city || ""}
              onChange={(e) => handleInputChange("city", e.target.value)}
            />
          </div>
          <div>
            <label className="block text-sm font-medium mb-1">State</label>
            <Input
              value={formData.state || ""}
              onChange={(e) => handleInputChange("state", e.target.value)}
            />
          </div>
          <div>
            <label className="block text-sm font-medium mb-1">PIN Code</label>
            <Input
              value={formData.pincode || ""}
              onChange={(e) => handleInputChange("pincode", e.target.value)}
            />
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
