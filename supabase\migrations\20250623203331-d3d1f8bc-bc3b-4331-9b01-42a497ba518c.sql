
-- Create customers table
CREATE TABLE public.customers (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  name TEXT NOT NULL,
  phone TEXT NOT NULL,
  email TEXT,
  address TEXT,
  city TEXT,
  state TEXT,
  pincode TEXT,
  alternate_phone TEXT,
  customer_type TEXT DEFAULT 'Individual',
  credit_limit DECIMAL(10,2) DEFAULT 0,
  payment_terms TEXT DEFAULT 'Cash',
  tax_id TEXT,
  preferred_contact TEXT DEFAULT 'Phone',
  notes TEXT,
  last_service_date DATE,
  total_spent DECIMAL(10,2) DEFAULT 0,
  user_id UUID NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Create jobsheets table
CREATE TABLE public.jobsheets (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  customer_id UUID REFERENCES public.customers(id),
  customer_name TEXT NOT NULL,
  phone TEXT NOT NULL,
  alternate_phone TEXT,
  email TEXT,
  address TEXT,
  device_type TEXT NOT NULL CHECK (device_type IN ('Laptop', 'Desktop', 'Mobile', 'Tablet', 'Other')),
  device_brand TEXT NOT NULL,
  device_model TEXT NOT NULL,
  serial_number TEXT,
  password TEXT,
  reported_issues TEXT NOT NULL,
  physical_condition TEXT NOT NULL,
  accessories_received TEXT,
  estimated_cost DECIMAL(10,2),
  advance_payment DECIMAL(10,2),
  expected_delivery DATE,
  technician_assigned TEXT,
  priority TEXT NOT NULL CHECK (priority IN ('Low', 'Medium', 'High', 'Urgent')),
  status TEXT NOT NULL CHECK (status IN ('Received', 'In Progress', 'Completed', 'Delivered', 'Cancelled')),
  warranty_terms TEXT,
  special_instructions TEXT,
  received_by TEXT NOT NULL,
  customer_signature BOOLEAN DEFAULT false,
  terms_accepted BOOLEAN DEFAULT false,
  date DATE NOT NULL DEFAULT CURRENT_DATE,
  user_id UUID NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Create services table
CREATE TABLE public.services (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  jobsheet_id UUID REFERENCES public.jobsheets(id),
  customer_id UUID REFERENCES public.customers(id),
  customer TEXT NOT NULL,
  phone TEXT NOT NULL,
  device TEXT NOT NULL,
  issue TEXT NOT NULL,
  status TEXT NOT NULL CHECK (status IN ('Pending', 'In Progress', 'Completed', 'On Hold', 'Cancelled')),
  service_type TEXT DEFAULT 'Repair' CHECK (service_type IN ('Repair', 'Maintenance', 'Installation', 'Consultation', 'Upgrade')),
  priority TEXT DEFAULT 'Medium' CHECK (priority IN ('Low', 'Medium', 'High', 'Urgent')),
  technician TEXT,
  estimated_completion DATE,
  actual_completion DATE,
  warranty_period INTEGER DEFAULT 0,
  cost DECIMAL(10,2) DEFAULT 0,
  parts_cost DECIMAL(10,2) DEFAULT 0,
  labor_cost DECIMAL(10,2) DEFAULT 0,
  user_id UUID NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Create invoices table
CREATE TABLE public.invoices (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  service_id UUID REFERENCES public.services(id),
  customer_id UUID REFERENCES public.customers(id),
  customer TEXT NOT NULL,
  amount DECIMAL(10,2) NOT NULL,
  date DATE NOT NULL DEFAULT CURRENT_DATE,
  status TEXT DEFAULT 'Pending' CHECK (status IN ('Draft', 'Pending', 'Paid', 'Overdue', 'Cancelled')),
  device TEXT,
  device_type TEXT,
  custom_device_name TEXT,
  issue TEXT,
  phone TEXT,
  alternate_phone TEXT,
  email TEXT,
  address TEXT,
  city TEXT,
  state TEXT,
  pincode TEXT,
  expected_delivery DATE,
  inspection_fee DECIMAL(10,2) DEFAULT 500,
  billable_warranty TEXT,
  remarks TEXT,
  show_remarks BOOLEAN DEFAULT false,
  gst DECIMAL(10,2) DEFAULT 0,
  estimated_amount DECIMAL(10,2),
  tax_amount DECIMAL(10,2) DEFAULT 0,
  discount_amount DECIMAL(10,2) DEFAULT 0,
  due_date DATE,
  payment_method TEXT,
  payment_terms TEXT,
  notes TEXT,
  user_id UUID NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Create used_parts table for invoice parts
CREATE TABLE public.used_parts (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  invoice_id UUID REFERENCES public.invoices(id) ON DELETE CASCADE,
  item_id TEXT,
  item_name TEXT NOT NULL,
  quantity INTEGER NOT NULL,
  price DECIMAL(10,2) NOT NULL,
  is_custom BOOLEAN DEFAULT false,
  manufacturer TEXT,
  model TEXT,
  serial_no TEXT,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Create expenses table
CREATE TABLE public.expenses (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  invoice_id UUID REFERENCES public.invoices(id),
  date DATE NOT NULL DEFAULT CURRENT_DATE,
  vendor TEXT NOT NULL,
  description TEXT NOT NULL,
  amount DECIMAL(10,2) NOT NULL,
  category TEXT NOT NULL CHECK (category IN ('Office', 'Marketing', 'Equipment', 'Utilities', 'Travel', 'Parts', 'Labor', 'Shipping', 'Other')),
  receipt TEXT DEFAULT 'Available' CHECK (receipt IN ('Available', 'Pending', 'Missing')),
  tax_amount DECIMAL(10,2) DEFAULT 0,
  payment_method TEXT,
  reference_number TEXT,
  approved_by TEXT,
  department TEXT,
  user_id UUID NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Create comments table for universal commenting system
CREATE TABLE public.comments (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  entity_type TEXT NOT NULL CHECK (entity_type IN ('jobsheet', 'service', 'invoice', 'customer')),
  entity_id UUID NOT NULL,
  text TEXT NOT NULL,
  author TEXT NOT NULL,
  comment_type TEXT NOT NULL CHECK (comment_type IN ('edit', 'customer', 'internal', 'status_change')),
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  timestamp TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Enable Row Level Security
ALTER TABLE public.customers ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.jobsheets ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.services ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.invoices ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.used_parts ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.expenses ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.comments ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for customers
CREATE POLICY "Users can view their own customers" ON public.customers FOR SELECT USING (auth.uid()::text = user_id::text);
CREATE POLICY "Users can create their own customers" ON public.customers FOR INSERT WITH CHECK (auth.uid()::text = user_id::text);
CREATE POLICY "Users can update their own customers" ON public.customers FOR UPDATE USING (auth.uid()::text = user_id::text);
CREATE POLICY "Users can delete their own customers" ON public.customers FOR DELETE USING (auth.uid()::text = user_id::text);

-- Create RLS policies for jobsheets
CREATE POLICY "Users can view their own jobsheets" ON public.jobsheets FOR SELECT USING (auth.uid()::text = user_id::text);
CREATE POLICY "Users can create their own jobsheets" ON public.jobsheets FOR INSERT WITH CHECK (auth.uid()::text = user_id::text);
CREATE POLICY "Users can update their own jobsheets" ON public.jobsheets FOR UPDATE USING (auth.uid()::text = user_id::text);
CREATE POLICY "Users can delete their own jobsheets" ON public.jobsheets FOR DELETE USING (auth.uid()::text = user_id::text);

-- Create RLS policies for services
CREATE POLICY "Users can view their own services" ON public.services FOR SELECT USING (auth.uid()::text = user_id::text);
CREATE POLICY "Users can create their own services" ON public.services FOR INSERT WITH CHECK (auth.uid()::text = user_id::text);
CREATE POLICY "Users can update their own services" ON public.services FOR UPDATE USING (auth.uid()::text = user_id::text);
CREATE POLICY "Users can delete their own services" ON public.services FOR DELETE USING (auth.uid()::text = user_id::text);

-- Create RLS policies for invoices
CREATE POLICY "Users can view their own invoices" ON public.invoices FOR SELECT USING (auth.uid()::text = user_id::text);
CREATE POLICY "Users can create their own invoices" ON public.invoices FOR INSERT WITH CHECK (auth.uid()::text = user_id::text);
CREATE POLICY "Users can update their own invoices" ON public.invoices FOR UPDATE USING (auth.uid()::text = user_id::text);
CREATE POLICY "Users can delete their own invoices" ON public.invoices FOR DELETE USING (auth.uid()::text = user_id::text);

-- Create RLS policies for used_parts
CREATE POLICY "Users can view used_parts through invoices" ON public.used_parts FOR SELECT USING (
  EXISTS (SELECT 1 FROM public.invoices WHERE invoices.id = used_parts.invoice_id AND auth.uid()::text = invoices.user_id::text)
);
CREATE POLICY "Users can create used_parts through invoices" ON public.used_parts FOR INSERT WITH CHECK (
  EXISTS (SELECT 1 FROM public.invoices WHERE invoices.id = used_parts.invoice_id AND auth.uid()::text = invoices.user_id::text)
);
CREATE POLICY "Users can update used_parts through invoices" ON public.used_parts FOR UPDATE USING (
  EXISTS (SELECT 1 FROM public.invoices WHERE invoices.id = used_parts.invoice_id AND auth.uid()::text = invoices.user_id::text)
);
CREATE POLICY "Users can delete used_parts through invoices" ON public.used_parts FOR DELETE USING (
  EXISTS (SELECT 1 FROM public.invoices WHERE invoices.id = used_parts.invoice_id AND auth.uid()::text = invoices.user_id::text)
);

-- Create RLS policies for expenses
CREATE POLICY "Users can view their own expenses" ON public.expenses FOR SELECT USING (auth.uid()::text = user_id::text);
CREATE POLICY "Users can create their own expenses" ON public.expenses FOR INSERT WITH CHECK (auth.uid()::text = user_id::text);
CREATE POLICY "Users can update their own expenses" ON public.expenses FOR UPDATE USING (auth.uid()::text = user_id::text);
CREATE POLICY "Users can delete their own expenses" ON public.expenses FOR DELETE USING (auth.uid()::text = user_id::text);

-- Create RLS policies for comments
CREATE POLICY "Users can view comments for their entities" ON public.comments FOR SELECT USING (
  CASE 
    WHEN entity_type = 'customer' THEN EXISTS (SELECT 1 FROM public.customers WHERE customers.id = comments.entity_id AND auth.uid()::text = customers.user_id::text)
    WHEN entity_type = 'jobsheet' THEN EXISTS (SELECT 1 FROM public.jobsheets WHERE jobsheets.id = comments.entity_id AND auth.uid()::text = jobsheets.user_id::text)
    WHEN entity_type = 'service' THEN EXISTS (SELECT 1 FROM public.services WHERE services.id = comments.entity_id AND auth.uid()::text = services.user_id::text)
    WHEN entity_type = 'invoice' THEN EXISTS (SELECT 1 FROM public.invoices WHERE invoices.id = comments.entity_id AND auth.uid()::text = invoices.user_id::text)
    ELSE false
  END
);
CREATE POLICY "Users can create comments for their entities" ON public.comments FOR INSERT WITH CHECK (
  CASE 
    WHEN entity_type = 'customer' THEN EXISTS (SELECT 1 FROM public.customers WHERE customers.id = comments.entity_id AND auth.uid()::text = customers.user_id::text)
    WHEN entity_type = 'jobsheet' THEN EXISTS (SELECT 1 FROM public.jobsheets WHERE jobsheets.id = comments.entity_id AND auth.uid()::text = jobsheets.user_id::text)
    WHEN entity_type = 'service' THEN EXISTS (SELECT 1 FROM public.services WHERE services.id = comments.entity_id AND auth.uid()::text = services.user_id::text)
    WHEN entity_type = 'invoice' THEN EXISTS (SELECT 1 FROM public.invoices WHERE invoices.id = comments.entity_id AND auth.uid()::text = invoices.user_id::text)
    ELSE false
  END
);

-- Create indexes for better performance
CREATE INDEX idx_customers_user_id ON public.customers(user_id);
CREATE INDEX idx_jobsheets_user_id ON public.jobsheets(user_id);
CREATE INDEX idx_jobsheets_customer_id ON public.jobsheets(customer_id);
CREATE INDEX idx_services_user_id ON public.services(user_id);
CREATE INDEX idx_services_jobsheet_id ON public.services(jobsheet_id);
CREATE INDEX idx_services_customer_id ON public.services(customer_id);
CREATE INDEX idx_invoices_user_id ON public.invoices(user_id);
CREATE INDEX idx_invoices_service_id ON public.invoices(service_id);
CREATE INDEX idx_invoices_customer_id ON public.invoices(customer_id);
CREATE INDEX idx_used_parts_invoice_id ON public.used_parts(invoice_id);
CREATE INDEX idx_expenses_user_id ON public.expenses(user_id);
CREATE INDEX idx_expenses_invoice_id ON public.expenses(invoice_id);
CREATE INDEX idx_comments_entity ON public.comments(entity_type, entity_id);

-- Create function to calculate expected delivery date (3 business days from creation)
CREATE OR REPLACE FUNCTION calculate_expected_delivery(start_date DATE DEFAULT CURRENT_DATE)
RETURNS DATE
LANGUAGE plpgsql
AS $$
DECLARE
    result_date DATE := start_date;
    days_added INTEGER := 0;
BEGIN
    WHILE days_added < 3 LOOP
        result_date := result_date + 1;
        -- Skip weekends (Saturday = 6, Sunday = 0)
        IF EXTRACT(DOW FROM result_date) NOT IN (0, 6) THEN
            days_added := days_added + 1;
        END IF;
    END LOOP;
    RETURN result_date;
END;
$$;

-- Create trigger function to auto-update expected_delivery when jobsheet is created
CREATE OR REPLACE FUNCTION update_jobsheet_expected_delivery()
RETURNS TRIGGER
LANGUAGE plpgsql
AS $$
BEGIN
    IF NEW.expected_delivery IS NULL THEN
        NEW.expected_delivery := calculate_expected_delivery(NEW.date);
    END IF;
    RETURN NEW;
END;
$$;

-- Create trigger for jobsheets
CREATE TRIGGER trigger_update_jobsheet_expected_delivery
    BEFORE INSERT OR UPDATE ON public.jobsheets
    FOR EACH ROW
    EXECUTE FUNCTION update_jobsheet_expected_delivery();

-- Create trigger function to update customer total_spent when invoice is paid
CREATE OR REPLACE FUNCTION update_customer_total_spent()
RETURNS TRIGGER
LANGUAGE plpgsql
AS $$
BEGIN
    -- Update when invoice status changes to 'Paid'
    IF NEW.status = 'Paid' AND (OLD.status IS NULL OR OLD.status != 'Paid') THEN
        UPDATE public.customers 
        SET total_spent = COALESCE(total_spent, 0) + NEW.amount,
            last_service_date = NEW.date
        WHERE id = NEW.customer_id;
    -- Subtract when invoice status changes from 'Paid' to something else
    ELSIF OLD.status = 'Paid' AND NEW.status != 'Paid' THEN
        UPDATE public.customers 
        SET total_spent = COALESCE(total_spent, 0) - OLD.amount
        WHERE id = OLD.customer_id;
    END IF;
    RETURN NEW;
END;
$$;

-- Create trigger for invoice status changes
CREATE TRIGGER trigger_update_customer_total_spent
    AFTER UPDATE ON public.invoices
    FOR EACH ROW
    EXECUTE FUNCTION update_customer_total_spent();
